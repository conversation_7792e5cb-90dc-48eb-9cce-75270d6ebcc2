// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'AppStore.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AppStore on _AppStore, Store {
  late final _$sliderListAtom =
      Atom(name: '_AppStore.sliderList', context: context);

  @override
  List<SliderModel> get sliderList {
    _$sliderListAtom.reportRead();
    return super.sliderList;
  }

  @override
  set sliderList(List<SliderModel> value) {
    _$sliderListAtom.reportWrite(value, super.sliderList, () {
      super.sliderList = value;
    });
  }

  late final _$categoryListAtom =
      Atom(name: '_AppStore.categoryList', context: context);

  @override
  List<CategoryModels> get categoryList {
    _$categoryListAtom.reportRead();
    return super.categoryList;
  }

  @override
  set categoryList(List<CategoryModels> value) {
    _$categoryListAtom.reportWrite(value, super.categoryList, () {
      super.categoryList = value;
    });
  }

  late final _$parentCategoryListAtom =
      Atom(name: '_AppStore.parentCategoryList', context: context);

  @override
  List<CategoryModels> get parentCategoryList {
    _$parentCategoryListAtom.reportRead();
    return super.parentCategoryList;
  }

  @override
  set parentCategoryList(List<CategoryModels> value) {
    _$parentCategoryListAtom.reportWrite(value, super.parentCategoryList, () {
      super.parentCategoryList = value;
    });
  }

  late final _$allPostAtom = Atom(name: '_AppStore.allPost', context: context);

  @override
  List<PostModel> get allPost {
    _$allPostAtom.reportRead();
    return super.allPost;
  }

  @override
  set allPost(List<PostModel> value) {
    _$allPostAtom.reportWrite(value, super.allPost, () {
      super.allPost = value;
    });
  }

  late final _$latestPostAtom =
      Atom(name: '_AppStore.latestPost', context: context);

  @override
  List<PostModel> get latestPost {
    _$latestPostAtom.reportRead();
    return super.latestPost;
  }

  @override
  set latestPost(List<PostModel> value) {
    _$latestPostAtom.reportWrite(value, super.latestPost, () {
      super.latestPost = value;
    });
  }

  late final _$homeLatestPostAtom =
      Atom(name: '_AppStore.homeLatestPost', context: context);

  @override
  List<PostModel> get homeLatestPost {
    _$homeLatestPostAtom.reportRead();
    return super.homeLatestPost;
  }

  @override
  set homeLatestPost(List<PostModel> value) {
    _$homeLatestPostAtom.reportWrite(value, super.homeLatestPost, () {
      super.homeLatestPost = value;
    });
  }

  late final _$featurePostAtom =
      Atom(name: '_AppStore.featurePost', context: context);

  @override
  List<PostModel> get featurePost {
    _$featurePostAtom.reportRead();
    return super.featurePost;
  }

  @override
  set featurePost(List<PostModel> value) {
    _$featurePostAtom.reportWrite(value, super.featurePost, () {
      super.featurePost = value;
    });
  }

  late final _$barterPostAtom =
      Atom(name: '_AppStore.barterPost', context: context);

  @override
  List<PostModel> get barterPost {
    _$barterPostAtom.reportRead();
    return super.barterPost;
  }

  @override
  set barterPost(List<PostModel> value) {
    _$barterPostAtom.reportWrite(value, super.barterPost, () {
      super.barterPost = value;
    });
  }

  late final _$electronicsAtom =
      Atom(name: '_AppStore.electronics', context: context);

  @override
  List<PostModel> get electronics {
    _$electronicsAtom.reportRead();
    return super.electronics;
  }

  @override
  set electronics(List<PostModel> value) {
    _$electronicsAtom.reportWrite(value, super.electronics, () {
      super.electronics = value;
    });
  }

  late final _$vehiclesAtom =
      Atom(name: '_AppStore.vehicles', context: context);

  @override
  List<PostModel> get vehicles {
    _$vehiclesAtom.reportRead();
    return super.vehicles;
  }

  @override
  set vehicles(List<PostModel> value) {
    _$vehiclesAtom.reportWrite(value, super.vehicles, () {
      super.vehicles = value;
    });
  }

  late final _$homeElectronicsAtom =
      Atom(name: '_AppStore.homeElectronics', context: context);

  @override
  List<PostModel> get homeElectronics {
    _$homeElectronicsAtom.reportRead();
    return super.homeElectronics;
  }

  @override
  set homeElectronics(List<PostModel> value) {
    _$homeElectronicsAtom.reportWrite(value, super.homeElectronics, () {
      super.homeElectronics = value;
    });
  }

  late final _$homeVehiclesAtom =
      Atom(name: '_AppStore.homeVehicles', context: context);

  @override
  List<PostModel> get homeVehicles {
    _$homeVehiclesAtom.reportRead();
    return super.homeVehicles;
  }

  @override
  set homeVehicles(List<PostModel> value) {
    _$homeVehiclesAtom.reportWrite(value, super.homeVehicles, () {
      super.homeVehicles = value;
    });
  }

  late final _$homeFeaturedPostAtom =
      Atom(name: '_AppStore.homeFeaturedPost', context: context);

  @override
  List<PostModel> get homeFeaturedPost {
    _$homeFeaturedPostAtom.reportRead();
    return super.homeFeaturedPost;
  }

  @override
  set homeFeaturedPost(List<PostModel> value) {
    _$homeFeaturedPostAtom.reportWrite(value, super.homeFeaturedPost, () {
      super.homeFeaturedPost = value;
    });
  }

  late final _$userPostsAtom =
      Atom(name: '_AppStore.userPosts', context: context);

  @override
  List<PostModel> get userPosts {
    _$userPostsAtom.reportRead();
    return super.userPosts;
  }

  @override
  set userPosts(List<PostModel> value) {
    _$userPostsAtom.reportWrite(value, super.userPosts, () {
      super.userPosts = value;
    });
  }

  late final _$favListAtom = Atom(name: '_AppStore.favList', context: context);

  @override
  List<PostModel> get favList {
    _$favListAtom.reportRead();
    return super.favList;
  }

  @override
  set favList(List<PostModel> value) {
    _$favListAtom.reportWrite(value, super.favList, () {
      super.favList = value;
    });
  }

  late final _$cityAtom = Atom(name: '_AppStore.city', context: context);

  @override
  List<CityModels> get city {
    _$cityAtom.reportRead();
    return super.city;
  }

  @override
  set city(List<CityModels> value) {
    _$cityAtom.reportWrite(value, super.city, () {
      super.city = value;
    });
  }

  late final _$mainCityAtom =
      Atom(name: '_AppStore.mainCity', context: context);

  @override
  List<CityModels> get mainCity {
    _$mainCityAtom.reportRead();
    return super.mainCity;
  }

  @override
  set mainCity(List<CityModels> value) {
    _$mainCityAtom.reportWrite(value, super.mainCity, () {
      super.mainCity = value;
    });
  }

  late final _$siteDetailsAtom =
      Atom(name: '_AppStore.siteDetails', context: context);

  @override
  List<SiteModel> get siteDetails {
    _$siteDetailsAtom.reportRead();
    return super.siteDetails;
  }

  @override
  set siteDetails(List<SiteModel> value) {
    _$siteDetailsAtom.reportWrite(value, super.siteDetails, () {
      super.siteDetails = value;
    });
  }

  late final _$loginedAtom = Atom(name: '_AppStore.logined', context: context);

  @override
  bool get logined {
    _$loginedAtom.reportRead();
    return super.logined;
  }

  @override
  set logined(bool value) {
    _$loginedAtom.reportWrite(value, super.logined, () {
      super.logined = value;
    });
  }

  late final _$user_nameAtom =
      Atom(name: '_AppStore.user_name', context: context);

  @override
  String? get user_name {
    _$user_nameAtom.reportRead();
    return super.user_name;
  }

  @override
  set user_name(String? value) {
    _$user_nameAtom.reportWrite(value, super.user_name, () {
      super.user_name = value;
    });
  }

  late final _$user_emailAtom =
      Atom(name: '_AppStore.user_email', context: context);

  @override
  String? get user_email {
    _$user_emailAtom.reportRead();
    return super.user_email;
  }

  @override
  set user_email(String? value) {
    _$user_emailAtom.reportWrite(value, super.user_email, () {
      super.user_email = value;
    });
  }

  late final _$profile_imageAtom =
      Atom(name: '_AppStore.profile_image', context: context);

  @override
  String? get profile_image {
    _$profile_imageAtom.reportRead();
    return super.profile_image;
  }

  @override
  set profile_image(String? value) {
    _$profile_imageAtom.reportWrite(value, super.profile_image, () {
      super.profile_image = value;
    });
  }

  late final _$user_mobileAtom =
      Atom(name: '_AppStore.user_mobile', context: context);

  @override
  String? get user_mobile {
    _$user_mobileAtom.reportRead();
    return super.user_mobile;
  }

  @override
  set user_mobile(String? value) {
    _$user_mobileAtom.reportWrite(value, super.user_mobile, () {
      super.user_mobile = value;
    });
  }

  late final _$firebase_keyAtom =
      Atom(name: '_AppStore.firebase_key', context: context);

  @override
  String? get firebase_key {
    _$firebase_keyAtom.reportRead();
    return super.firebase_key;
  }

  @override
  set firebase_key(String? value) {
    _$firebase_keyAtom.reportWrite(value, super.firebase_key, () {
      super.firebase_key = value;
    });
  }

  late final _$user_instagramAtom =
      Atom(name: '_AppStore.user_instagram', context: context);

  @override
  String? get user_instagram {
    _$user_instagramAtom.reportRead();
    return super.user_instagram;
  }

  @override
  set user_instagram(String? value) {
    _$user_instagramAtom.reportWrite(value, super.user_instagram, () {
      super.user_instagram = value;
    });
  }

  late final _$user_x_comAtom =
      Atom(name: '_AppStore.user_x_com', context: context);

  @override
  String? get user_x_com {
    _$user_x_comAtom.reportRead();
    return super.user_x_com;
  }

  @override
  set user_x_com(String? value) {
    _$user_x_comAtom.reportWrite(value, super.user_x_com, () {
      super.user_x_com = value;
    });
  }

  late final _$user_facebookAtom =
      Atom(name: '_AppStore.user_facebook', context: context);

  @override
  String? get user_facebook {
    _$user_facebookAtom.reportRead();
    return super.user_facebook;
  }

  @override
  set user_facebook(String? value) {
    _$user_facebookAtom.reportWrite(value, super.user_facebook, () {
      super.user_facebook = value;
    });
  }

  late final _$user_idAtom = Atom(name: '_AppStore.user_id', context: context);

  @override
  int? get user_id {
    _$user_idAtom.reportRead();
    return super.user_id;
  }

  @override
  set user_id(int? value) {
    _$user_idAtom.reportWrite(value, super.user_id, () {
      super.user_id = value;
    });
  }

  late final _$conditionAtom =
      Atom(name: '_AppStore.condition', context: context);

  @override
  List<dynamic> get condition {
    _$conditionAtom.reportRead();
    return super.condition;
  }

  @override
  set condition(List<dynamic> value) {
    _$conditionAtom.reportWrite(value, super.condition, () {
      super.condition = value;
    });
  }

  late final _$pageViewControllerAtom =
      Atom(name: '_AppStore.pageViewController', context: context);

  @override
  PageController? get pageViewController {
    _$pageViewControllerAtom.reportRead();
    return super.pageViewController;
  }

  @override
  set pageViewController(PageController? value) {
    _$pageViewControllerAtom.reportWrite(value, super.pageViewController, () {
      super.pageViewController = value;
    });
  }

  late final _$currentIndexAtom =
      Atom(name: '_AppStore.currentIndex', context: context);

  @override
  int get currentIndex {
    _$currentIndexAtom.reportRead();
    return super.currentIndex;
  }

  @override
  set currentIndex(int value) {
    _$currentIndexAtom.reportWrite(value, super.currentIndex, () {
      super.currentIndex = value;
    });
  }

  late final _$currentSortByAtom =
      Atom(name: '_AppStore.currentSortBy', context: context);

  @override
  String? get currentSortBy {
    _$currentSortByAtom.reportRead();
    return super.currentSortBy;
  }

  @override
  set currentSortBy(String? value) {
    _$currentSortByAtom.reportWrite(value, super.currentSortBy, () {
      super.currentSortBy = value;
    });
  }

  late final _$currentPriceRangeAtom =
      Atom(name: '_AppStore.currentPriceRange', context: context);

  @override
  String? get currentPriceRange {
    _$currentPriceRangeAtom.reportRead();
    return super.currentPriceRange;
  }

  @override
  set currentPriceRange(String? value) {
    _$currentPriceRangeAtom.reportWrite(value, super.currentPriceRange, () {
      super.currentPriceRange = value;
    });
  }

  late final _$currentBarterFilterAtom =
      Atom(name: '_AppStore.currentBarterFilter', context: context);

  @override
  String? get currentBarterFilter {
    _$currentBarterFilterAtom.reportRead();
    return super.currentBarterFilter;
  }

  @override
  set currentBarterFilter(String? value) {
    _$currentBarterFilterAtom.reportWrite(value, super.currentBarterFilter, () {
      super.currentBarterFilter = value;
    });
  }

  late final _$currentConditionAtom =
      Atom(name: '_AppStore.currentCondition', context: context);

  @override
  String? get currentCondition {
    _$currentConditionAtom.reportRead();
    return super.currentCondition;
  }

  @override
  set currentCondition(String? value) {
    _$currentConditionAtom.reportWrite(value, super.currentCondition, () {
      super.currentCondition = value;
    });
  }

  late final _$currentCityAtom =
      Atom(name: '_AppStore.currentCity', context: context);

  @override
  String? get currentCity {
    _$currentCityAtom.reportRead();
    return super.currentCity;
  }

  @override
  set currentCity(String? value) {
    _$currentCityAtom.reportWrite(value, super.currentCity, () {
      super.currentCity = value;
    });
  }

  late final _$currentAreaAtom =
      Atom(name: '_AppStore.currentArea', context: context);

  @override
  String? get currentArea {
    _$currentAreaAtom.reportRead();
    return super.currentArea;
  }

  @override
  set currentArea(String? value) {
    _$currentAreaAtom.reportWrite(value, super.currentArea, () {
      super.currentArea = value;
    });
  }

  late final _$storeUserDataAsyncAction =
      AsyncAction('_AppStore.storeUserData', context: context);

  @override
  Future<dynamic> storeUserData() {
    return _$storeUserDataAsyncAction.run(() => super.storeUserData());
  }

  late final _$logoutAsyncAction =
      AsyncAction('_AppStore.logout', context: context);

  @override
  Future<void> logout() {
    return _$logoutAsyncAction.run(() => super.logout());
  }

  late final _$loginStoreAsyncAction =
      AsyncAction('_AppStore.loginStore', context: context);

  @override
  Future<void> loginStore(dynamic name, dynamic id, dynamic msg, dynamic email,
      dynamic user_image, dynamic mobile, dynamic context) {
    return _$loginStoreAsyncAction.run(() =>
        super.loginStore(name, id, msg, email, user_image, mobile, context));
  }

  late final _$loginStoreOptimizedAsyncAction =
      AsyncAction('_AppStore.loginStoreOptimized', context: context);

  @override
  Future<void> loginStoreOptimized(dynamic name, dynamic id, dynamic msg,
      dynamic email, dynamic user_image, dynamic mobile, dynamic context) {
    return _$loginStoreOptimizedAsyncAction.run(() => super.loginStoreOptimized(
        name, id, msg, email, user_image, mobile, context));
  }

  late final _$updateProfileAsyncAction =
      AsyncAction('_AppStore.updateProfile', context: context);

  @override
  Future<void> updateProfile(dynamic name, dynamic email, dynamic user_image,
      dynamic mobile, dynamic context,
      {String? instagram, String? xCom, String? facebook}) {
    return _$updateProfileAsyncAction.run(() => super.updateProfile(
        name, email, user_image, mobile, context,
        instagram: instagram, xCom: xCom, facebook: facebook));
  }

  late final _$refreshPostsForCurrentUserAsyncAction =
      AsyncAction('_AppStore.refreshPostsForCurrentUser', context: context);

  @override
  Future<void> refreshPostsForCurrentUser() {
    return _$refreshPostsForCurrentUserAsyncAction
        .run(() => super.refreshPostsForCurrentUser());
  }

  late final _$manualRefreshPostsAsyncAction =
      AsyncAction('_AppStore.manualRefreshPosts', context: context);

  @override
  Future<void> manualRefreshPosts() {
    return _$manualRefreshPostsAsyncAction
        .run(() => super.manualRefreshPosts());
  }

  late final _$_AppStoreActionController =
      ActionController(name: '_AppStore', context: context);

  @override
  void filterIndex(int index) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.filterIndex');
    try {
      return super.filterIndex(index);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void applyFilters(
      {String? sortBy,
      String? priceRange,
      String? barterFilter,
      String? condition,
      String? city,
      String? area}) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.applyFilters');
    try {
      return super.applyFilters(
          sortBy: sortBy,
          priceRange: priceRange,
          barterFilter: barterFilter,
          condition: condition,
          city: city,
          area: area);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearFilters() {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.clearFilters');
    try {
      return super.clearFilters();
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _filterPostList() {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore._filterPostList');
    try {
      return super._filterPostList();
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _resetPostLists() {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore._resetPostLists');
    try {
      return super._resetPostLists();
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSliderData(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setSliderData');
    try {
      return super.setSliderData(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSiteData(dynamic data) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.setSiteData');
    try {
      return super.setSiteData(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setCategoryData(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setCategoryData');
    try {
      return super.setCategoryData(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic getMainCategoryData(int parent_id) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.getMainCategoryData');
    try {
      return super.getMainCategoryData(parent_id);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setCityData(dynamic data) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.setCityData');
    try {
      return super.setCityData(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic getMainCityData(int parent_id) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.getMainCityData');
    try {
      return super.getMainCityData(parent_id);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setAllPost(dynamic data) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.setAllPost');
    try {
      return super.setAllPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setNewPost(PostModel data) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.setNewPost');
    try {
      return super.setNewPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setUpdatePost(PostModel data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setUpdatePost');
    try {
      return super.setUpdatePost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  List<PostModel> cateroryPostList(dynamic id, bool isMain) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.cateroryPostList');
    try {
      return super.cateroryPostList(id, isMain);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setLatestPost(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setLatestPost');
    try {
      return super.setLatestPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void deletePost(dynamic post_id) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.deletePost');
    try {
      return super.deletePost(post_id);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setFeaturePost(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setFeaturePost');
    try {
      return super.setFeaturePost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setBarterPost(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setBarterPost');
    try {
      return super.setBarterPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setElecPost(dynamic data) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.setElecPost');
    try {
      return super.setElecPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setVehiclesPost(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setVehiclesPost');
    try {
      return super.setVehiclesPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setFavPost(dynamic data) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.setFavPost');
    try {
      return super.setFavPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void removeFavPost(int data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.removeFavPost');
    try {
      return super.removeFavPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void getFavPost(dynamic data) {
    final _$actionInfo =
        _$_AppStoreActionController.startAction(name: '_AppStore.getFavPost');
    try {
      return super.getFavPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setItemConditions(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setItemConditions');
    try {
      return super.setItemConditions(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void StoreFirebasekey(dynamic key) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.StoreFirebasekey');
    try {
      return super.StoreFirebasekey(key);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearUserSpecificData() {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.clearUserSpecificData');
    try {
      return super.clearUserSpecificData();
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void updateUserPostsForCurrentUser() {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.updateUserPostsForCurrentUser');
    try {
      return super.updateUserPostsForCurrentUser();
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  List<PostModel> getFilteredCategoryPosts(
      int categoryId, bool isMainCategory) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.getFilteredCategoryPosts');
    try {
      return super.getFilteredCategoryPosts(categoryId, isMainCategory);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
sliderList: ${sliderList},
categoryList: ${categoryList},
parentCategoryList: ${parentCategoryList},
allPost: ${allPost},
latestPost: ${latestPost},
homeLatestPost: ${homeLatestPost},
featurePost: ${featurePost},
barterPost: ${barterPost},
electronics: ${electronics},
vehicles: ${vehicles},
homeElectronics: ${homeElectronics},
homeVehicles: ${homeVehicles},
homeFeaturedPost: ${homeFeaturedPost},
userPosts: ${userPosts},
favList: ${favList},
city: ${city},
mainCity: ${mainCity},
siteDetails: ${siteDetails},
logined: ${logined},
user_name: ${user_name},
user_email: ${user_email},
profile_image: ${profile_image},
user_mobile: ${user_mobile},
firebase_key: ${firebase_key},
user_instagram: ${user_instagram},
user_x_com: ${user_x_com},
user_facebook: ${user_facebook},
user_id: ${user_id},
condition: ${condition},
pageViewController: ${pageViewController},
currentIndex: ${currentIndex},
currentSortBy: ${currentSortBy},
currentPriceRange: ${currentPriceRange},
currentBarterFilter: ${currentBarterFilter},
currentCondition: ${currentCondition},
currentCity: ${currentCity},
currentArea: ${currentArea}
    ''';
  }
}
