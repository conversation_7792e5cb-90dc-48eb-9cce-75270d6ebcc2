import 'dart:convert';

import 'package:http/http.dart' as http;

import 'package:shared_preferences/shared_preferences.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/CategoryModel.dart';
import 'package:soho_souk/models/FavModel.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'package:soho_souk/models/SiteModel.dart';
import 'package:soho_souk/models/SliderModel.dart';
import 'package:soho_souk/models/CityModel.dart';

class ServiceApi {
  Future<List<SliderModel>> getSlider() async {
      List<SliderModel> list =[];
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-app-slider"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
       list = (json.decode(response.body) as List)
            .map((data) => new SliderModel.fromJson(data))
            .toList();
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }
  Future<List<CategoryModels>> getCategory() async {
      List<CategoryModels> list =[];
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-category"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
       list = (json.decode(response.body) as List)
            .map((data) => new CategoryModels.fromJson(data))
            .toList();
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }
  Future<List<PostModel>> getLatestPost(String url) async {
      List<PostModel> list =[];
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}${url}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
       list = (json.decode(response.body) as List)
            .map((data) => new PostModel.fromJson(data))
            .toList();
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }
  Future<List<PostModel>> getAllPost() async {
      List<PostModel> list =[];
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-all-post"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
       list = (json.decode(response.body) as List)
            .map((data) => new PostModel.fromJson(data))
            .toList();
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }

  Future<List<PostModel>>getCategoryPost(int id) async {
      List<PostModel> list =[];
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-category-post-list/${id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
    
       list = (json.decode(response.body) as List)
            .map((data) => new PostModel.fromJson(data))
            .toList();
          
       
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }

  Future<List<CityModels>> getCity() async {
      List<CityModels> list =[];
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-city"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
       list = (json.decode(response.body) as List)
            .map((data) => new CityModels.fromJson(data))
            .toList();
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }
  Future<List<PostModel>> getFav() async {
      List<PostModel> list =[];
      if(appStore.user_id==null){
        
        return list;
      }
    try {
     
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-favourite-post/${appStore.user_id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
       list = (json.decode(response.body) as List)
            .map((data) => new PostModel.fromJson(data))
            .toList();
             //print('get-favourite-post/${list.length}');
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }
  Future<List> getCondition() async {
      List list =[];
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-item-condition"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
       list = json.decode(response.body);
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }

  Future<List<SiteModel>> getSiteDetails() async {
      List<SiteModel> list =[];
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-site-details"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
       list = (json.decode(response.body) as List)
            .map((data) => new SiteModel.fromJson(data))
            .toList();
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
   return list;
  }
}
