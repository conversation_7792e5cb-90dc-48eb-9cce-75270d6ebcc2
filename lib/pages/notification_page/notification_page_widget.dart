import 'dart:convert';

import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_util.dart';
import 'package:soho_souk/main.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:soho_souk/models/UserPostMessage.dart';
import 'package:soho_souk/store/AppStore.dart';
import '../chat_details_page/chat_details_page_widget.dart';

import '../../Classified_App/classified_app_model.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../app_state.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'package:http/http.dart' as http;
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'notification_page_model.dart';
export 'notification_page_model.dart';

class NotificationPageWidget extends StatefulWidget {
  const NotificationPageWidget({super.key});

  @override
  State<NotificationPageWidget> createState() => _NotificationPageWidgetState();
}

class _NotificationPageWidgetState extends State<NotificationPageWidget>
    with TickerProviderStateMixin {
  late NotificationPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    if(appStore.logined)
    fetchNotifications();
    _model = createModel(context, () => NotificationPageModel());
    
    _model.tabBarController = TabController(
      vsync: this,
      length: 2,
      initialIndex: 0,
    )..addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }
  List<PushNotification> list =[];
void fetchNotifications() async {


    try {

      http.get(Uri.parse("${ApiUtils.BASE_URL}get-push-notification/${appStore.user_id}"),
    headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          }
   ).then((response) {
      if (response.statusCode == 200) {
        // Map mapValue = json.decode(response.body);
        setState(() {
        List<dynamic> data = json.decode(response.body);
        print("data ${data}");
        list = data.map((json) => PushNotification.fromJson(json)).toList();
          
       
        });
      }
    });
      
    } catch (e) {
      print('Error fetching notifications: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              // Compact header with integrated tabs
              Container(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 8.0),
                child: Column(
                  children: [
                    Text(
                      FFLocalizations.of(context).getText(
                        'g881f0w5' /* Notifications */,
                      ),
                      style: ClassifiedAppTheme.of(context).bodyMedium.override(
                            fontFamily: 'Satoshi',
                            fontSize: 22.0,
                            fontWeight: FontWeight.bold,
                            useGoogleFonts: false,
                          ),
                    ),
                    SizedBox(height: 16.0),
                    // Compact custom tab bar
                    Container(
                      decoration: BoxDecoration(
                        color: ClassifiedAppTheme.of(context).secondaryBackground,
                        borderRadius: BorderRadius.circular(25.0),
                        boxShadow: [
                          BoxShadow(
                            blurRadius: 4.0,
                            color: Color(0x10000000),
                            offset: Offset(0.0, 2.0),
                          )
                        ],
                      ),
                      padding: EdgeInsets.all(4.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                _model.tabBarController?.animateTo(0);
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                                decoration: BoxDecoration(
                                  color: _model.tabBarCurrentIndex == 0
                                      ? ClassifiedAppTheme.of(context).primary
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      'assets/images/message.svg',
                                      width: 18.0,
                                      height: 18.0,
                                      color: _model.tabBarCurrentIndex == 0 
                                          ? Colors.white
                                          : Color(0xFF7B7676),
                                    ),
                                    SizedBox(width: 6.0),
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        'zvoju99a' /* Chat */,
                                      ),
                                      style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                        fontFamily: 'Satoshi',
                                        color: _model.tabBarCurrentIndex == 0
                                            ? Colors.white
                                            : Color(0xFF7B7676),
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                _model.tabBarController?.animateTo(1);
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
                                decoration: BoxDecoration(
                                  color: _model.tabBarCurrentIndex == 1
                                      ? ClassifiedAppTheme.of(context).primary
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      'assets/images/notification.svg',
                                      width: 18.0,
                                      height: 18.0,
                                      color: _model.tabBarCurrentIndex == 1 
                                          ? Colors.white
                                          : Color(0xFF7B7676),
                                    ),
                                    SizedBox(width: 6.0),
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        'g881f0w5' /* Alerts */,
                                      ),
                                      style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                        fontFamily: 'Satoshi',
                                        color: _model.tabBarCurrentIndex == 1
                                            ? Colors.white
                                            : Color(0xFF7B7676),
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    Expanded(
                      child: TabBarView(
                        controller: _model.tabBarController,
                        children: [
                          // Chat Tab Content
                          _buildChatTab(),
                          // Notification Tab Content
                          _buildNotificationTab(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChatTab() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(12.0, 8.0, 12.0, 0.0),
      child: appStore.user_id == null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/images/emptyChat.png',
                    width: 100.0,
                    height: 100.0,
                    fit: BoxFit.cover,
                  ),
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                    child: Text(
                      FFLocalizations.of(context).getText(
                        'login_required' /* Please login to view chats */,
                      ),
                      style: ClassifiedAppTheme.of(context).headlineSmall,
                    ),
                  ),
                ],
              ),
            )
          : StreamBuilder<QuerySnapshot>(
              key: ValueKey('chat_stream_user1_${appStore.user_id}'),
              stream: appStore.user_id != null
                  ? FirebaseFirestore.instance
                      .collection('user_post_message')
                      .where('user1Id', isEqualTo: appStore.user_id.toString())
                      .orderBy('lastMessageTime', descending: true)
                      .snapshots()
                  : Stream.empty(),
              builder: (context, snapshot1) {
                return StreamBuilder<QuerySnapshot>(
                  key: ValueKey('chat_stream_user2_${appStore.user_id}'),
                  stream: appStore.user_id != null
                      ? FirebaseFirestore.instance
                          .collection('user_post_message')
                          .where('user2Id', isEqualTo: appStore.user_id.toString())
                          .orderBy('lastMessageTime', descending: true)
                          .snapshots()
                      : Stream.empty(),
                  builder: (context, snapshot2) {
                    if (appStore.user_id == null) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              FFLocalizations.of(context).getText(
                                'login_required' /* Please login to view chats */,
                              ),
                              style: ClassifiedAppTheme.of(context).headlineSmall,
                            ),
                          ],
                        ),
                      );
                    }

                    if (snapshot1.connectionState == ConnectionState.waiting ||
                        snapshot2.connectionState == ConnectionState.waiting) {
                      return Center(
                        child: SizedBox(
                          width: 50.0,
                          height: 50.0,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              ClassifiedAppTheme.of(context).primary,
                            ),
                          ),
                        ),
                      );
                    }

                    List<UserPostMessage> allChats = [];
                    List<UserPostMessage> sortedChats = [];
                    
                    try {
                      if (snapshot1.hasData && snapshot1.data != null) {
                        allChats.addAll(snapshot1.data!.docs.map((doc) => 
                            UserPostMessage.fromMap(doc.data() as Map<String, dynamic>, doc.id)));
                      }
                      
                      if (snapshot2.hasData && snapshot2.data != null) {
                        allChats.addAll(snapshot2.data!.docs.map((doc) => 
                            UserPostMessage.fromMap(doc.data() as Map<String, dynamic>, doc.id)));
                      }

                      Map<String, UserPostMessage> uniqueChats = {};
                      for (var chat in allChats) {
                        if (chat.roomId != null && chat.roomId!.isNotEmpty) {
                          if (!uniqueChats.containsKey(chat.roomId) ||
                              (chat.lastMessageTime?.millisecondsSinceEpoch ?? 0) >
                                  (uniqueChats[chat.roomId]?.lastMessageTime?.millisecondsSinceEpoch ?? 0)) {
                            uniqueChats[chat.roomId!] = chat;
                          }
                        }
                      }

                      sortedChats = uniqueChats.values.toList();
                      sortedChats.sort((a, b) => 
                          (b.lastMessageTime?.millisecondsSinceEpoch ?? 0)
                              .compareTo(a.lastMessageTime?.millisecondsSinceEpoch ?? 0));
                    } catch (e) {
                      print('Error processing chat data: $e');
                      sortedChats = [];
                    }

                    if (sortedChats.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'assets/images/emptyChat.png',
                              width: 100.0,
                              height: 100.0,
                              fit: BoxFit.cover,
                            ),
                            Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                              child: Text(
                                FFLocalizations.of(context).getText(
                                  'no_chats' /* No chats yet */,
                                ),
                                style: ClassifiedAppTheme.of(context).headlineSmall,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.separated(
                      padding: EdgeInsets.fromLTRB(0, 8.0, 0, 16.0),
                      scrollDirection: Axis.vertical,
                      itemCount: sortedChats.length,
                      separatorBuilder: (_, __) => SizedBox(height: 12.0),
                      itemBuilder: (context, index) {
                        final chat = sortedChats[index];
                        return InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ChatDetailsPageWidget(
                                  vendor_id: int.tryParse(chat.user1Id == appStore.user_id.toString() 
                                      ? chat.user2Id ?? '0' 
                                      : chat.user1Id ?? '0') ?? 0,
                                  vendor_name: chat.user1Id == appStore.user_id.toString() 
                                      ? chat.user2Name ?? 'Unknown' 
                                      : chat.user1Name ?? 'Unknown',
                                  user_post_message_id: chat.id,
                                ),
                              ),
                            );
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).secondaryBackground,
                              boxShadow: [
                                BoxShadow(
                                  blurRadius: 4.0,
                                  color: Color(0x33000000),
                                  offset: Offset(0.0, 2.0),
                                )
                              ],
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(12.0, 12.0, 12.0, 12.0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 48.0,
                                    height: 48.0,
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context).primary,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.person,
                                      color: Colors.white,
                                      size: 24.0,
                                    ),
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 0.0, 0.0),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            chat.user1Id == appStore.user_id.toString() 
                                                ? chat.user2Name ?? 'Unknown' 
                                                : chat.user1Name ?? 'Unknown',
                                            style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                              fontFamily: 'Satoshi',
                                              color: Color(0xFF030401),
                                              fontSize: 16.0,
                                              fontWeight: FontWeight.bold,
                                              useGoogleFonts: false,
                                            ),
                                          ),
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment: CrossAxisAlignment.end,
                                            children: [
                                              Expanded(
                                                child: Padding(
                                                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 0.0, 0.0),
                                                  child: Text(
                                                    chat.lastMessage ?? 'No messages yet',
                                                    style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'Satoshi',
                                                      color: ClassifiedAppTheme.of(context).secondaryText,
                                                      fontSize: 13.0,
                                                      fontWeight: FontWeight.w500,
                                                      useGoogleFonts: false,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Text(
                                                 chat.lastMessageTime != null 
                                                     ? _formatTime(chat.lastMessageTime!.toDate())
                                                     : '',
                                                 textAlign: TextAlign.center,
                                                 style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                                   fontFamily: 'Satoshi',
                                                   color: ClassifiedAppTheme.of(context).secondaryText,
                                                   fontSize: 12.0,
                                                   fontWeight: FontWeight.w500,
                                                   useGoogleFonts: false,
                                                 ),
                                               ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
    );
  }

  Widget _buildNotificationTab() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(12.0, 8.0, 12.0, 0.0),
      child: Builder(
        builder: (context) {
          return ListView.separated(
            padding: EdgeInsets.fromLTRB(0, 0.0, 0, 16.0),
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            itemCount: list.length,
            separatorBuilder: (_, __) => SizedBox(height: 10.0),
            itemBuilder: (context, notificationListIndex) {
              final notificationListItem = list[notificationListIndex];
              return Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: ClassifiedAppTheme.of(context).secondaryBackground,
                    boxShadow: [
                      BoxShadow(
                        blurRadius: 4.0,
                        color: Color(0x32000000),
                        offset: Offset(0.0, 2.0),
                      )
                    ],
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(12.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 36.0,
                          height: 36.0,
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context).secondaryBackground,
                            boxShadow: [
                              BoxShadow(
                                blurRadius: 4.0,
                                color: Color(0x33000000),
                                offset: Offset(0.0, 2.0),
                              )
                            ],
                            shape: BoxShape.circle,
                          ),
                          alignment: AlignmentDirectional(0.0, 0.0),
                          child: SvgPicture.asset(
                            'assets/images/notification_primary.svg',
                            width: 20.0,
                            height: 20.0,
                            fit: BoxFit.contain,
                            color: ClassifiedAppTheme.of(context).primary,
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 0.0, 0.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                AutoSizeText(
                                  notificationListItem.title,
                                  textAlign: TextAlign.start,
                                  maxLines: 2,
                                  style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                                  minFontSize: 12.0,
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 0.0, 0.0),
                                  child: Text(
                                    notificationListItem.description,
                                    style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Satoshi',
                                      color: ClassifiedAppTheme.of(context).secondaryText,
                                      fontSize: 14.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}
class PushNotification {

  final String title;
  final String description;

  PushNotification({

    required this.title,
    required this.description,
  });

  factory PushNotification.fromJson(Map<String, dynamic> json) {
    return PushNotification(

      title: json['title'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
   
      'title': title,
      'description': description,
    };
  }
}