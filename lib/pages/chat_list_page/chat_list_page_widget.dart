import '/backend/schema/structs/index.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '../../Classified_App/classified_app_widgets.dart';
import '../../Classified_App/internationalization.dart';
import '../chat_details_page/chat_details_page_widget.dart';
import '../../models/UserPostMessage.dart';
import '../../store/AppStore.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ChatListPageWidget extends StatefulWidget {
  const ChatListPageWidget({Key? key}) : super(key: key);

  @override
  _ChatListPageWidgetState createState() => _ChatListPageWidgetState();
}

class _ChatListPageWidgetState extends State<ChatListPageWidget> {
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final appStore = AppStore();

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: ClassifiedAppTheme.of(context).primary,
          automaticallyImplyLeading: false,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_rounded,
              color: Colors.white,
              size: 30.0,
            ),
            onPressed: () async {
              context.pop();
            },
          ),
          title: Text(
            FFLocalizations.of(context).getText(
              'chat_list' /* My Chats */,
            ),
            style: ClassifiedAppTheme.of(context).headlineMedium.override(
                  fontFamily: 'Satoshi',
                  color: Colors.white,
                  fontSize: 22.0,
                  fontWeight: FontWeight.w600,
                  useGoogleFonts: false,
                ),
          ),
          centerTitle: false,
          elevation: 2.0,
        ),
        body: SafeArea(
          top: true,
          child: Padding(
            padding: EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
            child: appStore.user_id == null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/emptyChat.png',
                          width: 100.0,
                          height: 100.0,
                          fit: BoxFit.cover,
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'login_required' /* Please login to view chats */,
                            ),
                            style: ClassifiedAppTheme.of(context).headlineSmall,
                          ),
                        ),
                      ],
                    ),
                  )
                : StreamBuilder<QuerySnapshot>(
              key: ValueKey('chat_stream_user1_${appStore.user_id}'),
              stream: appStore.user_id != null
                  ? FirebaseFirestore.instance
                      .collection('user_post_message')
                      .where('user1Id', isEqualTo: appStore.user_id.toString())
                      .orderBy('lastMessageTime', descending: true)
                      .snapshots()
                  : Stream.empty(),
              builder: (context, snapshot1) {
                return StreamBuilder<QuerySnapshot>(
                  key: ValueKey('chat_stream_user2_${appStore.user_id}'),
                  stream: appStore.user_id != null
                      ? FirebaseFirestore.instance
                          .collection('user_post_message')
                          .where('user2Id', isEqualTo: appStore.user_id.toString())
                          .orderBy('lastMessageTime', descending: true)
                          .snapshots()
                      : Stream.empty(),
                  builder: (context, snapshot2) {
                    // Handle null user_id case
                    if (appStore.user_id == null) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              FFLocalizations.of(context).getText(
                                'login_required' /* Please login to view chats */,
                              ),
                              style: ClassifiedAppTheme.of(context).headlineSmall,
                            ),
                          ],
                        ),
                      );
                    }

                    if (snapshot1.connectionState == ConnectionState.waiting ||
                        snapshot2.connectionState == ConnectionState.waiting) {
                      return Center(
                        child: SizedBox(
                          width: 50.0,
                          height: 50.0,
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              ClassifiedAppTheme.of(context).primary,
                            ),
                          ),
                        ),
                      );
                    }

                    List<UserPostMessage> allChats = [];
                    List<UserPostMessage> sortedChats = [];
                    
                    try {
                      // Combine chats from both streams
                      if (snapshot1.hasData && snapshot1.data != null) {
                        allChats.addAll(snapshot1.data!.docs.map((doc) => 
                            UserPostMessage.fromMap(doc.data() as Map<String, dynamic>, doc.id)));
                      }
                      
                      if (snapshot2.hasData && snapshot2.data != null) {
                        allChats.addAll(snapshot2.data!.docs.map((doc) => 
                            UserPostMessage.fromMap(doc.data() as Map<String, dynamic>, doc.id)));
                      }

                      // Remove duplicates and sort by last message time
                      Map<String, UserPostMessage> uniqueChats = {};
                      for (var chat in allChats) {
                        if (chat.roomId != null && chat.roomId!.isNotEmpty) {
                          if (!uniqueChats.containsKey(chat.roomId) ||
                              (chat.lastMessageTime?.millisecondsSinceEpoch ?? 0) >
                                  (uniqueChats[chat.roomId]?.lastMessageTime?.millisecondsSinceEpoch ?? 0)) {
                            uniqueChats[chat.roomId!] = chat;
                          }
                        }
                      }

                      sortedChats = uniqueChats.values.toList();
                      sortedChats.sort((a, b) => 
                          (b.lastMessageTime?.millisecondsSinceEpoch ?? 0)
                              .compareTo(a.lastMessageTime?.millisecondsSinceEpoch ?? 0));
                    } catch (e) {
                      print('Error processing chat data: $e');
                      sortedChats = [];
                    }

                    if (sortedChats.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              'assets/images/chat-empty.svg',
                              width: 100.0,
                              height: 100.0,
                              fit: BoxFit.cover,
                            ),
                            Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                              child: Text(
                                FFLocalizations.of(context).getText(
                                  'no_chats' /* No chats yet */,
                                ),
                                style: ClassifiedAppTheme.of(context).headlineSmall,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                              child: Text(
                                FFLocalizations.of(context).getText(
                                  'start_chatting' /* Start chatting with sellers */,
                                ),
                                style: ClassifiedAppTheme.of(context).bodyMedium,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.separated(
                      key: ValueKey('chat_list_${appStore.user_id}_${sortedChats.length}'),
                      itemCount: sortedChats.length,
                      separatorBuilder: (context, index) => SizedBox(height: 12.0),
                      itemBuilder: (context, index) {
                        if (index >= sortedChats.length) {
                          return SizedBox.shrink();
                        }
                        UserPostMessage chat = sortedChats[index];
                        
                        // Add null safety checks
                        if (chat.id == null || appStore.user_id == null) {
                          return SizedBox.shrink();
                        }
                        
                        // Determine other user details
                        String currentUserId = appStore.user_id!.toString();
                        bool isCurrentUserUser1 = chat.user1Id == currentUserId;
                        String otherUserName = isCurrentUserUser1 ? 
                            (chat.user2Name ?? 'Unknown') : (chat.user1Name ?? 'Unknown');
                        String otherUserId = isCurrentUserUser1 ? 
                            (chat.user2Id ?? '') : (chat.user1Id ?? '');
                        
                        // Get unread count for current user
                        int unreadCount = isCurrentUserUser1 ? 
                            (chat.unreadCountUser1 ?? 0) : (chat.unreadCountUser2 ?? 0);

                        return InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            // Mark messages as read when opening chat
                            await _markMessagesAsRead(chat.id!, isCurrentUserUser1);
                            
                            context.pushNamed(
                              'ChatDetailsPage',
                              queryParameters: {
                                'vendor_id': serializeParam(
                                  otherUserId,
                                  ParamType.String,
                                ),
                                'vendor_name': serializeParam(
                                  otherUserName,
                                  ParamType.String,
                                ),
                                'user_post_message_id': serializeParam(
                                  chat.id,
                                  ParamType.String,
                                ),
                              }.withoutNulls,
                              extra: <String, dynamic>{
                                kTransitionInfoKey: TransitionInfo(
                                  hasTransition: true,
                                  transitionType: PageTransitionType.rightToLeft,
                                  duration: Duration(milliseconds: 300),
                                ),
                              },
                            );
                          },
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).secondaryBackground,
                              boxShadow: [
                                BoxShadow(
                                  blurRadius: 4.0,
                                  color: Color(0x33000000),
                                  offset: Offset(0.0, 2.0),
                                )
                              ],
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Container(
                                    width: 50.0,
                                    height: 50.0,
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context).primary,
                                      shape: BoxShape.circle,
                                    ),
                                    alignment: AlignmentDirectional(0.0, 0.0),
                                    child: Text(
                                      otherUserName.isNotEmpty ? otherUserName[0].toUpperCase() : 'U',
                                      style: ClassifiedAppTheme.of(context).titleLarge.override(
                                            fontFamily: 'Satoshi',
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 8.0, 0.0),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                child: Text(
                                                  otherUserName,
                                                  style: ClassifiedAppTheme.of(context).bodyLarge.override(
                                                        fontFamily: 'Satoshi',
                                                        fontWeight: FontWeight.w600,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                              ),
                                              if (chat.lastMessageTime != null)
                                                Text(
                                                  _formatTime(chat.lastMessageTime!),
                                                  style: ClassifiedAppTheme.of(context).bodySmall.override(
                                                        fontFamily: 'Satoshi',
                                                        color: ClassifiedAppTheme.of(context).secondaryText,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                            ],
                                          ),
                                          if (chat.postName != null)
                                            Padding(
                                              padding: EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 0.0, 0.0),
                                              child: Text(
                                                'About: ${chat.postName}',
                                                style: ClassifiedAppTheme.of(context).bodySmall.override(
                                                      fontFamily: 'Satoshi',
                                                      color: ClassifiedAppTheme.of(context).primary,
                                                      useGoogleFonts: false,
                                                    ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          if (chat.lastMessage != null)
                                            Padding(
                                              padding: EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 0.0, 0.0),
                                              child: Text(
                                                chat.lastMessage!,
                                                style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                                      fontFamily: 'Satoshi',
                                                      color: ClassifiedAppTheme.of(context).secondaryText,
                                                      useGoogleFonts: false,
                                                    ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  if (unreadCount > 0)
                                    Container(
                                      width: 24.0,
                                      height: 24.0,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context).error,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: AlignmentDirectional(0.0, 0.0),
                                      child: Text(
                                        unreadCount > 99 ? '99+' : unreadCount.toString(),
                                        style: ClassifiedAppTheme.of(context).bodySmall.override(
                                              fontFamily: 'Satoshi',
                                              color: Colors.white,
                                              fontSize: 10.0,
                                              fontWeight: FontWeight.w600,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _markMessagesAsRead(String userPostMessageId, bool isCurrentUserUser1) async {
    try {
      // Add null check for user_id
      if (appStore.user_id == null) {
        print('Cannot mark messages as read: user not logged in');
        return;
      }
      
      Map<String, dynamic> updateData = {};
      if (isCurrentUserUser1) {
        updateData['unreadCountUser1'] = 0;
      } else {
        updateData['unreadCountUser2'] = 0;
      }
      
      await FirebaseFirestore.instance
          .collection('user_post_message')
          .doc(userPostMessageId)
          .update(updateData);
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }

  String _formatTime(Timestamp timestamp) {
    DateTime dateTime = timestamp.toDate();
    DateTime now = DateTime.now();
    Duration difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }
}