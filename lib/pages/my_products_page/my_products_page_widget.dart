import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'package:http/http.dart' as http;
import 'package:soho_souk/pages/app_button/app_button_widget.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'my_products_page_model.dart';
export 'my_products_page_model.dart';

class MyProductsPageWidget extends StatefulWidget {
  const MyProductsPageWidget({super.key});

  @override
  State<MyProductsPageWidget> createState() => _MyProductsPageWidgetState();
}

class _MyProductsPageWidgetState extends State<MyProductsPageWidget> {
  late MyProductsPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  // Infinite scroll variables
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  final int _postsPerPage = 10;
  List<PostModel> _displayedPosts = [];
  bool _hasMorePosts = true;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => MyProductsPageModel());
    _initializePosts();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _model.dispose();
    super.dispose();
  }

  bool loader = true;

  getLatestPost() async {
    // Implementation for getting latest posts
  }

  void deletePost(post_id) async {
    showLoadingDialog(context);
    try {
      // Log the delete request
      print('Attempting to delete post with ID: $post_id');
      print('User ID: ${appStore.user_id}');
      print('API URL: ${ApiUtils.BASE_URL}delete-postad/${post_id}');

      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}delete-postad/${post_id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });

      // Log the response details
      print('Delete API Response Status: ${response.statusCode}');
      print('Delete API Response Body: ${response.body}');
      print('Delete API Response Headers: ${response.headers}');

      Navigator.of(context, rootNavigator: true).pop(false);

      if (response.statusCode == 200) {
        // Parse response body to check for actual success
        dynamic responseData;
        try {
          responseData = jsonDecode(response.body);
          print('Parsed response data: $responseData');
        } catch (e) {
          print('Failed to parse response body: $e');
        }

        // Check if the server actually confirmed deletion
        bool serverConfirmedDeletion = true;
        if (responseData != null && responseData is Map) {
          // Check for common API response patterns
          if (responseData.containsKey('success')) {
            serverConfirmedDeletion =
                responseData['success'] == true || responseData['success'] == 1;
          } else if (responseData.containsKey('status')) {
            serverConfirmedDeletion = responseData['status'] == 'success' ||
                responseData['status'] == true ||
                responseData['status'] == 1;
          } else if (responseData.containsKey('error')) {
            serverConfirmedDeletion = false;
            print('Server returned error: ${responseData['error']}');
          }
        }

        if (serverConfirmedDeletion) {
          // Update the app store only after server confirms deletion
          appStore.deletePost(post_id);

          // Remove from displayed posts
          setState(() {
            _displayedPosts.removeWhere((post) => post.id == post_id);

            // Check if we need to load more posts to maintain the list
            if (_displayedPosts.length < _postsPerPage && _hasMorePosts) {
              final allUserPosts = appStore.userPosts;
              final currentLength = _displayedPosts.length;

              if (currentLength < allUserPosts.length) {
                final nextPost = allUserPosts
                    .where((post) =>
                        !_displayedPosts.any((dp) => dp.id == post.id))
                    .take(1);
                if (nextPost.isNotEmpty) {
                  _displayedPosts.addAll(nextPost);
                }
              }
            }
          });

          toasty(context, "Post Deleted Successfully",
              bgColor: Colors.green, textColor: Colors.black);

          // Optional: Refresh posts from server to ensure sync
          _refreshPostsFromServer();
        } else {
          print('Server did not confirm deletion');
          toasty(
              context, "Failed to delete post: Server did not confirm deletion",
              bgColor: Colors.red, textColor: Colors.white);

          // Refresh to ensure UI is in sync with server
          _refreshPostsFromServer();
        }
      } else if (response.statusCode == 401) {
        print('Authentication error');
        toasty(context, "Authentication error. Please login again.",
            bgColor: Colors.red, textColor: Colors.white);
      } else if (response.statusCode == 403) {
        print('Authorization error');
        toasty(context, "You don't have permission to delete this post",
            bgColor: Colors.red, textColor: Colors.white);
      } else if (response.statusCode == 404) {
        print('Post not found');
        toasty(context, "Post not found or already deleted",
            bgColor: Colors.orange, textColor: Colors.white);
        // Remove from local state since it doesn't exist on server
        setState(() {
          _displayedPosts.removeWhere((post) => post.id == post_id);
        });
        appStore.deletePost(post_id);
      } else {
        print('Error Occurred - Status Code: ${response.statusCode}');
        toasty(context, "Failed to delete post (Error: ${response.statusCode})",
            bgColor: Colors.red, textColor: Colors.white);
      }
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop(false);
      setState(() {
        loader = false;
      });
      print('Error Occurred during delete: ${e.toString()}');
      print('Stack trace: ${StackTrace.current}');

      // Check if it's a network error
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection')) {
        toasty(context, "Network error. Please check your connection.",
            bgColor: Colors.red, textColor: Colors.white);
      } else {
        toasty(context, "Error: ${e.toString()}",
            bgColor: Colors.red, textColor: Colors.white);
      }
    }
  }

  // Add method to refresh posts from server
  Future<void> _refreshPostsFromServer() async {
    try {
      await appStore.refreshPostsForCurrentUser();
      _initializePosts();
    } catch (e) {
      print('Error refreshing posts: $e');
    }
  }

  // Initialize posts for infinite scroll
  void _initializePosts() {
    final allUserPosts = appStore.userPosts;
    _displayedPosts.clear();

    if (allUserPosts.isNotEmpty) {
      final endIndex = (_postsPerPage < allUserPosts.length)
          ? _postsPerPage
          : allUserPosts.length;
      _displayedPosts.addAll(allUserPosts.take(endIndex));
      _hasMorePosts = allUserPosts.length > _postsPerPage;
    } else {
      _hasMorePosts = false;
    }

    if (mounted) {
      setState(() {});
    }
  }

  // Handle scroll events for infinite scroll
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMorePosts();
    }
  }

  // Load more posts
  void _loadMorePosts() {
    if (_isLoadingMore || !_hasMorePosts) return;

    setState(() {
      _isLoadingMore = true;
    });

    // Simulate loading delay (you can remove this in production)
    Future.delayed(Duration(milliseconds: 500), () {
      final allUserPosts = appStore.userPosts;
      final currentLength = _displayedPosts.length;
      final remainingPosts = allUserPosts.length - currentLength;

      if (remainingPosts > 0) {
        final nextBatch = allUserPosts.skip(currentLength).take(_postsPerPage);
        _displayedPosts.addAll(nextBatch);

        // Check if there are more posts to load
        _hasMorePosts = _displayedPosts.length < allUserPosts.length;
      } else {
        _hasMorePosts = false;
      }

      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    // Reinitialize posts if the user posts have changed or list is empty
    if (_displayedPosts.isEmpty && appStore.userPosts.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializePosts();
      });
    }

    // Check if displayed posts contain posts that are no longer in userPosts
    final userPostIds = appStore.userPosts.map((post) => post.id).toSet();
    final displayedPostIds = _displayedPosts.map((post) => post.id).toSet();

    if (displayedPostIds.difference(userPostIds).isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _displayedPosts.removeWhere((post) => !userPostIds.contains(post.id));
        });
      });
    }

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              // Custom app bar with refresh button
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 21.0, 16.0, 0.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Back button
                    InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        context.safePop();
                      },
                      child: Container(
                        width: 44.0,
                        height: 44.0,
                        decoration: BoxDecoration(
                          color: ClassifiedAppTheme.of(context).tertiary,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: ClassifiedAppTheme.of(context).primaryText,
                          size: 20.0,
                        ),
                      ),
                    ),
                    // Title
                    Expanded(
                      child: Text(
                        'My Post ads',
                        textAlign: TextAlign.center,
                        style: ClassifiedAppTheme.of(context)
                            .bodyMedium
                            .override(
                              fontFamily: 'Satoshi',
                              color: ClassifiedAppTheme.of(context).primaryText,
                              fontSize: 20.0,
                              fontWeight: FontWeight.bold,
                              useGoogleFonts: false,
                            ),
                      ),
                    ),
                    // Refresh button
                    InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        // Show loading indicator
                        showLoadingDialog(context);

                        try {
                          // Refresh posts from server
                          await appStore.refreshPostsForCurrentUser();
                          _initializePosts();

                          Navigator.of(context, rootNavigator: true).pop();
                          toasty(context, "Posts refreshed successfully",
                              bgColor: Colors.green, textColor: Colors.black);
                        } catch (e) {
                          Navigator.of(context, rootNavigator: true).pop();
                          toasty(context, "Failed to refresh posts",
                              bgColor: Colors.red, textColor: Colors.white);
                        }
                      },
                      child: Container(
                        width: 44.0,
                        height: 44.0,
                        decoration: BoxDecoration(
                          color: ClassifiedAppTheme.of(context).tertiary,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.refresh,
                          color: ClassifiedAppTheme.of(context).primaryText,
                          size: 20.0,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _displayedPosts.isNotEmpty
                  ? Expanded(
                      child: Stack(
                        alignment: AlignmentDirectional(0.0, 1.0),
                        children: [
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                16.0, 0.0, 16.0, 0.0),
                            child: CustomScrollView(
                              controller: _scrollController,
                              slivers: [
                                SliverPadding(
                                  padding:
                                      EdgeInsets.fromLTRB(0, 20.0, 0, 20.0),
                                  sliver: SliverGrid(
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: () {
                                        if (MediaQuery.sizeOf(context).width <
                                            kBreakpointSmall) {
                                          return 2;
                                        } else if (MediaQuery.sizeOf(context)
                                                .width <
                                            kBreakpointMedium) {
                                          return 3;
                                        } else if (MediaQuery.sizeOf(context)
                                                .width <
                                            kBreakpointLarge) {
                                          return 4;
                                        } else {
                                          return 5;
                                        }
                                      }(),
                                      crossAxisSpacing: 12.0,
                                      mainAxisSpacing: 16.0,
                                      childAspectRatio: 0.75,
                                    ),
                                    delegate: SliverChildBuilderDelegate(
                                      (context, index) {
                                        final recentPost =
                                            _displayedPosts[index];
                                        return GestureDetector(
                                          onTap: () {
                                            context.pushNamed(
                                              'ProductDetailPage',
                                              queryParameters: {
                                                'post': jsonEncode(
                                                    recentPost.toJson()),
                                              }.withoutNulls,
                                              extra: <String, dynamic>{
                                                kTransitionInfoKey:
                                                    TransitionInfo(
                                                  hasTransition: true,
                                                  transitionType:
                                                      PageTransitionType
                                                          .rightToLeft,
                                                  duration: Duration(
                                                      milliseconds: 300),
                                                ),
                                              },
                                            );
                                          },
                                          child: Container(
                                            width: double.infinity,
                                            decoration: BoxDecoration(
                                              color:
                                                  ClassifiedAppTheme.of(context)
                                                      .secondaryBackground,
                                              boxShadow: [
                                                BoxShadow(
                                                  blurRadius: 8.0,
                                                  color: Color(0x15000000),
                                                  offset: Offset(0.0, 2.0),
                                                  spreadRadius: 0.0,
                                                )
                                              ],
                                              borderRadius:
                                                  BorderRadius.circular(16.0),
                                              border: Border.all(
                                                color: Color(0x08000000),
                                                width: 1.0,
                                              ),
                                            ),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Expanded(
                                                  flex: 3,
                                                  child: Stack(
                                                    children: [
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius.only(
                                                          topLeft:
                                                              Radius.circular(
                                                                  16.0),
                                                          topRight:
                                                              Radius.circular(
                                                                  16.0),
                                                        ),
                                                        child: Container(
                                                          width:
                                                              double.infinity,
                                                          height:
                                                              double.infinity,
                                                          child: recentPost
                                                                          .image !=
                                                                      null &&
                                                                  recentPost
                                                                      .image!
                                                                      .isNotEmpty
                                                              ? CachedNetworkImage(
                                                                  width: double
                                                                      .infinity,
                                                                  height: double
                                                                      .infinity,
                                                                  fit: BoxFit
                                                                      .cover,
                                                                  imageUrl:
                                                                      "${ApiUtils.post_image}${recentPost.image}",
                                                                  placeholder: (context,
                                                                          url) =>
                                                                      Container(
                                                                    color: Color(
                                                                        0xFFF5F5F5),
                                                                    child: Icon(
                                                                      Icons
                                                                          .image,
                                                                      color: Color(
                                                                          0xFF9E9E9E),
                                                                      size:
                                                                          32.0,
                                                                    ),
                                                                  ),
                                                                  errorWidget: (context,
                                                                          url,
                                                                          error) =>
                                                                      Container(
                                                                    color: Color(
                                                                        0xFFF5F5F5),
                                                                    child: Icon(
                                                                      Icons
                                                                          .image_not_supported,
                                                                      color: Color(
                                                                          0xFF9E9E9E),
                                                                      size:
                                                                          32.0,
                                                                    ),
                                                                  ),
                                                                )
                                                              : Container(
                                                                  color: Color(
                                                                      0xFFF5F5F5),
                                                                  child: Icon(
                                                                    Icons.image,
                                                                    color: Color(
                                                                        0xFF9E9E9E),
                                                                    size: 32.0,
                                                                  ),
                                                                ),
                                                        ),
                                                      ),
                                                      // Edit button - Left corner
                                                      Positioned(
                                                        top: 6.0,
                                                        left: 6.0,
                                                        child: GestureDetector(
                                                          onTap: () {
                                                            context.pushNamed(
                                                              'EditPost',
                                                              queryParameters: {
                                                                'id':
                                                                    serializeParam(
                                                                  recentPost.id,
                                                                  ParamType.int,
                                                                ),
                                                              }.withoutNulls,
                                                              extra: <String,
                                                                  dynamic>{
                                                                kTransitionInfoKey:
                                                                    TransitionInfo(
                                                                  hasTransition:
                                                                      true,
                                                                  transitionType:
                                                                      PageTransitionType
                                                                          .rightToLeft,
                                                                  duration: Duration(
                                                                      milliseconds:
                                                                          300),
                                                                ),
                                                              },
                                                            );
                                                          },
                                                          child: Container(
                                                            width: 28.0,
                                                            height: 28.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              color:
                                                                  Colors.white,
                                                              shape: BoxShape
                                                                  .circle,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  blurRadius:
                                                                      3.0,
                                                                  color: Color(
                                                                      0x15000000),
                                                                  offset:
                                                                      Offset(
                                                                          0.0,
                                                                          1.0),
                                                                )
                                                              ],
                                                            ),
                                                            child: Icon(
                                                              Icons.edit,
                                                              size: 14.0,
                                                              color: ClassifiedAppTheme
                                                                      .of(context)
                                                                  .primary,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      // Delete button - Right corner
                                                      Positioned(
                                                        top: 6.0,
                                                        right: 6.0,
                                                        child: GestureDetector(
                                                          onTap: () =>
                                                              showDialog(
                                                            context: context,
                                                            builder:
                                                                (dialogContext) {
                                                              return Dialog(
                                                                elevation: 0,
                                                                insetPadding:
                                                                    EdgeInsets
                                                                        .zero,
                                                                backgroundColor:
                                                                    Colors
                                                                        .transparent,
                                                                alignment: AlignmentDirectional(
                                                                        0.0,
                                                                        0.0)
                                                                    .resolve(
                                                                        Directionality.of(
                                                                            context)),
                                                                child:
                                                                    GestureDetector(
                                                                  onTap: () => _model
                                                                          .unfocusNode
                                                                          .canRequestFocus
                                                                      ? FocusScope.of(
                                                                              context)
                                                                          .requestFocus(_model
                                                                              .unfocusNode)
                                                                      : FocusScope.of(
                                                                              context)
                                                                          .unfocus(),
                                                                  child: deletePostWidget(
                                                                      _displayedPosts[index]
                                                                              .id ??
                                                                          0),
                                                                ),
                                                              );
                                                            },
                                                          ).then((value) =>
                                                                  setState(
                                                                      () {})),
                                                          child: Container(
                                                            width: 28.0,
                                                            height: 28.0,
                                                            decoration:
                                                                BoxDecoration(
                                                              color:
                                                                  Colors.white,
                                                              shape: BoxShape
                                                                  .circle,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  blurRadius:
                                                                      3.0,
                                                                  color: Color(
                                                                      0x15000000),
                                                                  offset:
                                                                      Offset(
                                                                          0.0,
                                                                          1.0),
                                                                )
                                                              ],
                                                            ),
                                                            child: Icon(
                                                              Icons.delete,
                                                              size: 14.0,
                                                              color: Colors.red,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                // Content section
                                                Expanded(
                                                  flex: 2,
                                                  child: Padding(
                                                    padding:
                                                        EdgeInsetsDirectional
                                                            .fromSTEB(12.0, 6.0,
                                                                12.0, 4.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        // Product name
                                                        AutoSizeText(
                                                          recentPost.post_name
                                                              .toString(),
                                                          maxLines: 1,
                                                          style:
                                                              ClassifiedAppTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'Satoshi',
                                                                    fontSize:
                                                                        12.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w600,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                          minFontSize: 10.0,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                        SizedBox(height: 1.0),
                                                        // Price
                                                        Text(
                                                          "AED ${recentPost.price}",
                                                          style:
                                                              ClassifiedAppTheme
                                                                      .of(context)
                                                                  .bodyMedium
                                                                  .override(
                                                                    fontFamily:
                                                                        'Satoshi',
                                                                    fontSize:
                                                                        13.0,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color: ClassifiedAppTheme.of(
                                                                            context)
                                                                        .primary,
                                                                    useGoogleFonts:
                                                                        false,
                                                                  ),
                                                        ),
                                                        SizedBox(height: 1.0),
                                                        // Vendor info
                                                        Flexible(
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .max,
                                                            children: [
                                                              ClipRRect(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            16.0),
                                                                child: recentPost
                                                                            .vendor_image ==
                                                                        ''
                                                                    ? Image
                                                                        .asset(
                                                                        'assets/images/soho_icon.jpg',
                                                                        width:
                                                                            20.0,
                                                                        height:
                                                                            20.0,
                                                                        fit: BoxFit
                                                                            .cover,
                                                                      )
                                                                    : CachedNetworkImage(
                                                                        width:
                                                                            20.0,
                                                                        height:
                                                                            20.0,
                                                                        fit: BoxFit
                                                                            .cover,
                                                                        imageUrl:
                                                                            "${ApiUtils.profile_files}${recentPost.vendor_image}",
                                                                        placeholder:
                                                                            (context, url) =>
                                                                                Container(
                                                                          color:
                                                                              Color(0xFFF5F5F5),
                                                                          child:
                                                                              Icon(
                                                                            Icons.person,
                                                                            color:
                                                                                Color(0xFF9E9E9E),
                                                                            size:
                                                                                16.0,
                                                                          ),
                                                                        ),
                                                                        errorWidget: (context,
                                                                                url,
                                                                                error) =>
                                                                            Container(
                                                                          color:
                                                                              Color(0xFFF5F5F5),
                                                                          child:
                                                                              Icon(
                                                                            Icons.person,
                                                                            color:
                                                                                Color(0xFF9E9E9E),
                                                                            size:
                                                                                16.0,
                                                                          ),
                                                                        ),
                                                                      ),
                                                              ),
                                                              SizedBox(
                                                                  width: 6.0),
                                                              Expanded(
                                                                child: Column(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Text(
                                                                      recentPost
                                                                          .vendor_name
                                                                          .toString(),
                                                                      style: ClassifiedAppTheme.of(
                                                                              context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily:
                                                                                'Satoshi',
                                                                            fontSize:
                                                                                11.0,
                                                                            fontWeight:
                                                                                FontWeight.w600,
                                                                            useGoogleFonts:
                                                                                false,
                                                                          ),
                                                                      maxLines:
                                                                          1,
                                                                      overflow:
                                                                          TextOverflow
                                                                              .ellipsis,
                                                                    ),
                                                                    Row(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .max,
                                                                      children: [
                                                                        Icon(
                                                                          Icons
                                                                              .location_on,
                                                                          color:
                                                                              Color(0xFF9E9E9E),
                                                                          size:
                                                                              12.0,
                                                                        ),
                                                                        SizedBox(
                                                                            width:
                                                                                2.0),
                                                                        Expanded(
                                                                          child:
                                                                              Text(
                                                                            recentPost.city.toString(),
                                                                            style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                                                                  fontFamily: 'Satoshi',
                                                                                  fontSize: 10.0,
                                                                                  fontWeight: FontWeight.w400,
                                                                                  color: Color(0xFF9E9E9E),
                                                                                  useGoogleFonts: false,
                                                                                ),
                                                                            maxLines:
                                                                                1,
                                                                            overflow:
                                                                                TextOverflow.ellipsis,
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      },
                                      childCount: _displayedPosts.length,
                                    ),
                                  ),
                                ),
                                // Loading indicator
                                if (_isLoadingMore)
                                  SliverToBoxAdapter(
                                    child: Padding(
                                      padding: EdgeInsets.all(16.0),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2.0,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            ClassifiedAppTheme.of(context)
                                                .primary,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                // Bottom padding
                                SliverToBoxAdapter(
                                  child: SizedBox(height: 80.0),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  : Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/myProductsEmpty.png',
                          width: 120.0,
                          height: 120.0,
                          fit: BoxFit.contain,
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 28.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'r8fti1o5' /* No products yet */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 24.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'tvaeeupe' /* Your products list is empty pl... */,
                            ),
                            textAlign: TextAlign.center,
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              54.0, 28.0, 54.0, 0.0),
                          child: AppButtonWidget(
                            text: 'Go to home',
                            action: () async {
                              context.goNamed('HomePage');
                            },
                          ),
                        ),
                      ],
                    )
            ],
          ),
        ),
      ),
    );
  }

  Widget deletePostWidget(int post_id) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.85,
        minWidth: 200.0,
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      margin: EdgeInsets.symmetric(horizontal: 20.0),
      decoration: BoxDecoration(
        color: ClassifiedAppTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: IntrinsicHeight(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 48.0,
              ),
              SizedBox(height: 16.0),
              Text(
                'Delete Post',
                style: ClassifiedAppTheme.of(context).headlineSmall.override(
                      fontFamily: 'Satoshi',
                      fontSize: 20.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
              SizedBox(height: 8.0),
              Flexible(
                child: Text(
                  'Are you sure you want to delete this post? This action cannot be undone.',
                  textAlign: TextAlign.center,
                  style: ClassifiedAppTheme.of(context).bodyMedium.override(
                        fontFamily: 'Satoshi',
                        fontSize: 14.0,
                        color: Color(0xFF6B7280),
                        useGoogleFonts: false,
                      ),
                  softWrap: true,
                  overflow: TextOverflow.visible,
                ),
              ),
              SizedBox(height: 20.0),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints: BoxConstraints(minHeight: 40.0),
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: Color(0xFFE5E7EB)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            padding: EdgeInsets.symmetric(
                                horizontal: 12.0, vertical: 8.0),
                            minimumSize: Size(80.0, 36.0),
                          ),
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              'Cancel',
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 14.0,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF6B7280),
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.0),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints: BoxConstraints(minHeight: 40.0),
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            deletePost(post_id);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            padding: EdgeInsets.symmetric(
                                horizontal: 12.0, vertical: 8.0),
                            minimumSize: Size(80.0, 36.0),
                          ),
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              'Delete',
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 14.0,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
