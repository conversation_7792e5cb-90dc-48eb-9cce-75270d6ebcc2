import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:photo_view/photo_view.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_util.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/CategoryModel.dart';
import 'package:soho_souk/models/FavModel.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'package:soho_souk/pages/product_detail_page/ReportBottomSheet.dart';
import 'package:soho_souk/pages/product_detail_page/hero_photo_view_wrapper.dart';
import 'package:soho_souk/widget/loader.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../Classified_App/classified_app_animations.dart';
import '../../Classified_App/classified_app_theme.dart';
import '/pages/app_button/app_button_widget.dart';
import '/pages/contact_us_bottom_sheet/contact_us_bottom_sheet_widget.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart'
    as smooth_page_indicator;
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'product_detail_page_model.dart';
export 'product_detail_page_model.dart';
import 'package:geocoding/geocoding.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong2;
class ProductDetailPageWidget extends StatefulWidget {
  //const ProductDetailPageWidget({super.key});
  ProductDetailPageWidget({
    super.key,
     this.post,
    this.related = false,
  });

  
  final PostModel? post;
  bool related;
  @override
  State<ProductDetailPageWidget> createState() =>
      _ProductDetailPageWidgetState();
}

class _ProductDetailPageWidgetState extends State<ProductDetailPageWidget>
    with TickerProviderStateMixin {
  late ProductDetailPageModel _model;
  latlong2.LatLng? _currentLocation = latlong2.LatLng(24.466667, 54.366669);
  final scaffoldKey = GlobalKey<ScaffoldState>();
   MapController _mapController = MapController();
  final animationsMap = {
    'columnOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 20.ms,
          duration: 400.ms,
          begin: 0.15,
          end: 1.0,
        ),
      ],
    ),
  };

  bool loader=false;
  List? images=[];
  //Map? list;
  String? category_name;
   getPostDetails() async {
    try {
      // final response = await http.get(
      //     Uri.parse("${ApiUtils.BASE_URL}get-post-details/${widget.post!.id}"),
      //     headers: {
      //       "Content-Type": "application/json",
      //       "Accept": "application/json",
      //       'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
      //     });
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-post-images/${widget.post!.id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
        
        setState(() {
       //list = json.decode(response.body);
     List image = json.decode(response.body);
     //image.map((e)=>images!.add(e['image'])).toList();
     image!.skip(1).map((e) => images!.add(e['image'])).toList();
     //list =data;
      //  images = image;
        //loader=false;
       // print("images ${images}");
        List<CategoryModels> category = appStore.categoryList.where((i)=>i.id ==widget.post?.category).toList();
      category_name =category[0].category_name;
      if (widget.post?.city != null && widget.post?.area != null) {
        setMapAddress(widget.post!.city!, widget.post!.area!);
      }
      getRelatedPost();
        });
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
  
  }

  
 List<PostModel> relatedPost =[];
  getRelatedPost() async{
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}related-products/${widget.post!.id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
        setState(() {
       relatedPost = (json.decode(response.body) as List)
            .map((data) => new PostModel.fromJson(data))
            .toList();
      
        });
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
  }

  String preprocessHtml(String html) {
  final regex = RegExp(r'style="[^"]*"'); // Matches the style attribute
  return html.replaceAll(regex, '');
}

  void saveFav(){
      showLoadingDialog(context);
     http
        .post(
      Uri.parse("${ApiUtils.BASE_URL}save-favourite-post"),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
      },
      body: jsonEncode(<String, dynamic>{
        'post_id':  widget.post!.id,
        'user_id':  appStore.user_id,
      }),
    )
        .then((response) {
             Navigator.of(context, rootNavigator: true).pop(false);
            Map lists = json.decode(response.body);
          if(response.statusCode==200){
            if(lists['status']==0){
              toasty(context, "${lists['message']}",
                bgColor: Colors.green, textColor: Colors.black);
                setState(() {
                  widget.post!.fav=true;
                });
              
                appStore.setFavPost(
                 widget.post!,
                );
            }else{
                setState(() {
                  widget.post!.fav=false;
                });
              
toasty(context, "${lists['message']}",
                bgColor: Colors.green, textColor: Colors.black);
                 appStore.removeFavPost(
                  widget.post!.id!
                );
            }
            
          }else{
            toasty(context, "Something wrong!",
                bgColor: Colors.green, textColor: Colors.black);
                // appStore.removeFavPost(
                //   FavModel.fromJson({
                //     'id': widget.post!.id,
                //     'image': images![0],
                //     'post_name': list!['title'],
                //     'city': list!['city'],
                //     'vendor_name': list!['vendor_name'],
                //    // 'vendor_image': list!['vendor_image']==null?'':list!['vendor_image'],
                //     'price': list!['price']
                //   })
                // );
          }
        });
  }

  @override
  void initState() {
    super.initState();
    images!.add(widget.post!.image);
    getPostDetails();
    _model = createModel(context, () => ProductDetailPageModel());

    _model.expandableController = ExpandableController(initialExpanded: false);
    print("widget.post!.fav ${widget.post!.fav}");
  }

  setMapAddress(String city,String area) async {
    List<Location> locations = await locationFromAddress('${area}, ${city}, uae');
    print("locations ${locations}");
    if (locations.isNotEmpty) {
      setState(() {
        _currentLocation = latlong2.LatLng(locations[0].latitude, locations[0].longitude);
      });
      _mapController.move(_currentLocation!, 13.0);
    }

  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: loader ? 
      Container(
        color: Colors.white,
        child: Center(
          child: SpinKitRipple(
                color: ClassifiedAppTheme.of(context).primary,
              ),
        ),
      ):
       Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Stack(
            alignment: AlignmentDirectional(0.0, 1.0),
            children: [
              SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Stack(
                      children: [
                        Container(
                          width: double.infinity,
                          height: 323.0,
                          child: Stack(
                            children: [
                              PageView(
                                controller: _model.pageViewController ??=
                                    PageController(initialPage: 0),
                                onPageChanged: (_) async {
                                  setState(() {
                                    FFAppState().productIndex =
                                        _model.pageViewCurrentIndex;
                                  });
                                },
                                scrollDirection: Axis.horizontal,
                                children: images!.map((e){
                                    int index = images!.indexOf(e);
                                    return GestureDetector(
                                       onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => HeroPhotoViewWrapper(
                              // minScale: PhotoViewComputedScale.contained * 0.7,
                              // maxScale: PhotoViewComputedScale.covered * 2.0,
                              imageUrls:images!,
                              initialIndex: index,

                //               CachedNetworkImage(
                //                              fit: BoxFit.cover,
                // width: double.infinity,
                //     height: double.infinity,
                                           
                //                             imageUrl:
                //                                 "${ApiUtils.product_image}${dealItem.app_image}",
                //                             placeholder: (context, url) => Padding(
                //                               padding: const EdgeInsets.all(35.0),
                //                               child: Center(child: CircularProgressIndicator()),
                //                             ),
                //                             errorWidget: (context, url, error) =>
                //                                 Icon(Icons.error),
                //                           )
                                  //NetworkImage("${ApiUtils.post_image}${e}"),
                            ),
                          )),
                                      child: Image.network("${ApiUtils.post_image}${e}", height: 323.0,width: double.infinity,
                                       fit: BoxFit.contain,),
                                      
                                      //  CachedNetworkImage(
                                      //   fadeInDuration:
                                      //     Duration(milliseconds: 500),
                                      //   fadeOutDuration:
                                      //     Duration(milliseconds: 500),
                                      //       width: double.infinity,
                                      // height: 323.0,
                                      // fit: BoxFit.contain,
                                      //       imageUrl:
                                      //         "${ApiUtils.post_image}${e}",
                                      //       placeholder: (context, url) => Padding(
                                      //       padding: const EdgeInsets.all(35.0),
                                      //       child: Center(child: CircularProgressIndicator()),
                                      //       ),
                                      //       errorWidget: (context, url, error) =>
                                      //         Icon(Icons.error),
                                      //     ),
                                    );
                                  
                                    
                                }).toList()
                                  
                                 
                              ),
                              Align(
                                alignment: AlignmentDirectional(0.0, 1.0),
                                child: Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 0.0, 16.0),
                                  child:
                                      smooth_page_indicator.SmoothPageIndicator(
                                    controller: _model.pageViewController ??=
                                        PageController(initialPage: 0),
                                    count: images!.length,
                                    axisDirection: Axis.horizontal,
                                    onDotClicked: (i) async {
                                      await _model.pageViewController!
                                          .animateToPage(
                                        i,
                                        duration: Duration(milliseconds: 500),
                                        curve: Curves.ease,
                                      );
                                    },
                                    effect:
                                        smooth_page_indicator.ExpandingDotsEffect(
                                      expansionFactor: 3.0,
                                      spacing: 8.0,
                                      radius: 16.0,
                                      dotWidth: 8.0,
                                      dotHeight: 8.0,
                                      dotColor: const Color.fromARGB(255, 146, 182, 245),
                                      activeDotColor: ClassifiedAppTheme.of(context)
                                          .primary,
                                      paintStyle: PaintingStyle.fill,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              16.0, 8.0, 16.0, 0.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  context.safePop();
                                },
                                child: Container(
                                  width: 36.0,
                                  height: 36.0,
                                  decoration: BoxDecoration(
                                    color: ClassifiedAppTheme.of(context)
                                        .primary,
                                    shape: BoxShape.circle,
                                  ),
                                  alignment: AlignmentDirectional(0.0, 0.0),
                                  child: SvgPicture.asset(
                                    'assets/images/arrow-left.svg',
                                    width: 20.0,
                                    height: 20.0,
                                    fit: BoxFit.contain,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              Column(
                         
                                children: [
                                  GestureDetector(
                                    onTap: (){
                                      if(appStore.logined){
                                            
                                        saveFav();
                                      }else{
                                         toasty(context, "Invalid Access, Please Login!",
                                          bgColor: Colors.red, textColor: Colors.black);
                                      }
                                    },
                                    child: Container(
                                      width: 36.0,
                                      height: 36.0,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryBackground,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: AlignmentDirectional(0.0, 0.0),
                                      child: SvgPicture.asset(
                                       widget.post!.fav ? 'assets/images/heart_filled.svg':'assets/images/heart.svg',
                                        width: 20.0,
                                        height: 20.0,
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 10,),
                                    InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  await showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              builder: (context) => ReportBottomSheet(postId: widget.post!.id!),
            );
                                },
                                child: Container(
                                  width: 36.0,
                                  height: 36.0,
                                  decoration: BoxDecoration(
                                    color: ClassifiedAppTheme.of(context)
                                        .primary,
                                    shape: BoxShape.circle,
                                  ),
                                  alignment: AlignmentDirectional(0.0, 0.0),
                                  child: Icon(Icons.report,color: Colors.white,),
                                    
                                  ),
                                ),
                                ],
                              ),
                                
                                  
                            ],
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding:
                          EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Expanded(
                                child: Text(
                                  "${widget.post!.post_name}",
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 20.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Text(
                                "AED  ${widget.post!.price}",
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 17.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 12.0, 0.0, 0.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                SvgPicture.asset(
                                  'assets/images/location.svg',
                                  width: 14.0,
                                  height: 14.0,
                                  fit: BoxFit.cover,
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      4.0, 0.0, 0.0, 0.0),
                                  child: Text(
                                    "${widget.post!.city}",
                                    style:
                                        ClassifiedAppTheme.of(context).bodyMedium,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 16.0, 0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                '5sc64sql' /* Product description */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 12.0, 0.0, 0.0),
                            child:
                            HtmlWidget("${widget.post!.description}",textStyle:ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    fontSize: 15.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),)
                            //  Text(
                            //   "${list!['description']}",
                            //   style: ClassifiedAppTheme.of(context)
                            //       .bodyMedium
                            //       .override(
                            //         fontFamily: 'Satoshi',
                            //         color: ClassifiedAppTheme.of(context)
                            //             .secondaryText,
                            //         fontSize: 15.0,
                            //         fontWeight: FontWeight.w500,
                            //         useGoogleFonts: false,
                            //       ),
                            // ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 16.0, 0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                'a1h9iatk' /* Product details */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                4.0, 12.0, 4.0, 0.0),
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: ClassifiedAppTheme.of(context)
                                    .secondaryBackground,
                                boxShadow: [
                                  BoxShadow(
                                    blurRadius: 4.0,
                                    color: Color(0x33000000),
                                    offset: Offset(0.0, 2.0),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            FFLocalizations.of(context).getText(
                                          'thjw9rom' /* Seller Name */,
                                        ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  fontSize: 15.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Text(
                                          "${widget.post!.vendor_name}",
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 15.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Text(
                                             FFLocalizations.of(context).getText(
                                          'k3bm4fpa' /* Item Conditions */,
                                        ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  fontSize: 15.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Text(
                                          "${widget.post!.item_conditions}",
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 17.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Text(
                                             FFLocalizations.of(context).getText(
                                          '3jr3k99b' /* Price */,
                                        ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  fontSize: 15.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Text(
                                          "${widget.post!.price}",
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 15.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            FFLocalizations.of(context).getText(
                                          'hz1dqng6' /* City */,
                                        ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  fontSize: 15.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Text(
                                          "${widget.post!.city}",
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 15.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            FFLocalizations.of(context).getText(
                                          'x139i2sq' /* x139i2sq */,
                                        ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  fontSize: 15.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Text(
                                          "${widget.post!.area}",
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 15.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Text(
                                             FFLocalizations.of(context).getText(
                                          '63tgxwaw' /* Category */,
                                        ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  fontSize: 15.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Text(
                                          "${category_name}",
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 15.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ],
                                    ),
                                  ].divide(SizedBox(height: 8.0)),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 12,),
                         SizedBox(
                          
                          height: 180,
                           child: Padding(
                             padding: const EdgeInsets.only(top: 10,right: 10,left: 10),
                             child: FlutterMap(
                              mapController: _mapController,
                                 options: MapOptions(
                                   center: _currentLocation,
                                   zoom: 13.0,
                                 ),
                                 children: [
                                   TileLayer(
                                     urlTemplate: "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png",
                                     subdomains: const ['a', 'b', 'c', 'd'],
                                     userAgentPackageName: 'com.sohosouk.app',
                                     maxZoom: 20,
                                     maxNativeZoom: 18,
                                   ),
                                   MarkerLayer(
                                     markers: [
                                       Marker(
                                         point: _currentLocation!,
                                         builder: (ctx) => Icon(
                                           Icons.location_pin,
                                           color: Colors.red,
                                           size: 40.0,
                                         ),
                                       ),
                                     ],
                                   ),
                                 ],
                               ),
                           ),
                         ),

            ElevatedButton(
              style: ElevatedButton.styleFrom(
                minimumSize: Size(double.infinity, 40), // Full width button
              ),
              onPressed: () async {
                print("https://www.google.com/maps/search/?api=1&query=${_currentLocation!.latitude},${_currentLocation!.longitude}");
                final Uri url = Uri.parse(
                  'https://www.google.com/maps/search/?api=1&query=${_currentLocation!.latitude},${_currentLocation!.longitude}',
                );
                if (await launchUrl(url)) {
                  await launchUrl(
                    url,
                    mode: LaunchMode.externalApplication, // Opens in external browser
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Could not open Google Maps')),
                  );
                }
              },
              child: Text(FFLocalizations.of(context).getText(
                                          '1e72j4wd' /* Open in Google Maps */,
                                        )),
            ),
                          
                          
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 16.0, 0.0, 0.0),
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: ClassifiedAppTheme.of(context).tertiary,
                                borderRadius: BorderRadius.circular(16.0),
                              ),
                              child: Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 16.0, 16.0, 0.0),
                                child: Container(
                                  width: double.infinity,
                                  color: ClassifiedAppTheme.of(context).tertiary,
                                  child: ExpandableNotifier(
                                    controller: _model.expandableController,
                                    child: ExpandablePanel(
                                      header: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          SvgPicture.asset(
                                            'assets/images/info-circle.svg',
                                            width: 24.0,
                                            height: 24.0,
                                            fit: BoxFit.cover,
                                          ),
                                          Padding(
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    12.0, 0.0, 0.0, 0.0),
                                            child: Text(
                                              FFLocalizations.of(context).getText(
                                                'q98owha5' /* Safety tips */,
                                              ),
                                              style: ClassifiedAppTheme.of(context)
                                                  .displaySmall
                                                  .override(
                                                    fontFamily: 'Satoshi',
                                                    fontSize: 18.0,
                                                    fontWeight: FontWeight.bold,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      collapsed: Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          color: ClassifiedAppTheme.of(context)
                                              .secondaryBackground,
                                        ),
                                      ),
                                      expanded: Column(
                                        mainAxisSize: MainAxisSize.max,
                                       
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                           Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 20.0, 0.0, 0.0),
                            child: Text(
                              "Staying Safe on Soho Souk",
                               textAlign: TextAlign.start,
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 18.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                                          Padding(
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    0.0, 0.0, 0.0, 16.0),
                                            child: HtmlWidget(
  preprocessHtml("${appStore.siteDetails[0].staying_safe}"),
  textStyle: ClassifiedAppTheme.of(context)
      .bodyMedium
      .override(
        fontFamily: 'Satoshi',
        color: ClassifiedAppTheme.of(context).secondaryText,
        fontSize: 14.0, // Set desired font size
        fontWeight: FontWeight.w500,
        useGoogleFonts: false,
      ),
),  
                                            //  Text(
                                            //   "One of our primary goals on Soho Souk is to ensure to the best of our ability that all users – sellers and buyers – can access and use our service in a safe and risk free manner. As a user there are also many ways you can keep yourself safe as well when either purchasing or selling an item. We’ve made our safe to do list which we encourage you to follow:\n-If you notice a suspicious account – whether it’s a seller or buyer – then please get in contact with us and we will have a look at that account and see what action to take. \n-If you find any accounts that are running scams, or if you yourself have fallen for any scam, then again get in contact with us and we will take the necessary action. \n-As a seller take extreme caution when contacted by an overseas number. Yes, occasionally there are some people who use an overseas number but that is rare. Most people that are looking to buy something from you as a seller will be living in the country and will also have a local number in use. \n-Don’t click on any third payment links or payment pages sent to you by another user. This applies to both sellers and buyers. \n-Do not under any circumstances divulge your financial and card details to any other user. \n-Always take caution when someone offers to use a courier service to collect whatever you are selling. Once again, don’t click on any third party pages to fill out some form. \n-Don’t send money to another user. If you are looking to buy something then carry out the transaction directly with that person. Do not send them the money over the Internet and most definitely do not fill out any payment details on a third party page they send you. Meet them in person and hand them the money directly once you ensure the item you want is ad advertised. \n-As mentioned above, make sure to check on the item you want to buy.",
                                            //   textAlign: TextAlign.justify,
                                            //   style: ClassifiedAppTheme.of(context)
                                            //       .bodyMedium
                                            //       .override(
                                            //         fontFamily: 'Satoshi',
                                            //         fontSize: 15.0,
                                            //         fontWeight: FontWeight.w500,
                                            //         useGoogleFonts: false,
                                            //         lineHeight: 1.5,
                                            //       ),
                                            // ),
                                          ),
                                          // Align(
                                          //   alignment:
                                          //       AlignmentDirectional(1.0, -1.0),
                                          //   child: Padding(
                                          //     padding:
                                          //         EdgeInsetsDirectional.fromSTEB(
                                          //             0.0, 0.0, 0.0, 10.0),
                                          //     child: Text(
                                          //       FFLocalizations.of(context)
                                          //           .getText(
                                          //         'jxh0acog' /* Read more */,
                                          //       ),
                                          //       style: ClassifiedAppTheme.of(
                                          //               context)
                                          //           .bodyMedium
                                          //           .override(
                                          //             fontFamily: 'Satoshi',
                                          //             color: ClassifiedAppTheme.of(
                                          //                     context)
                                          //                 .primary,
                                          //             fontSize: 15.0,
                                          //             fontWeight: FontWeight.bold,
                                          //             useGoogleFonts: false,
                                          //           ),
                                          //     ),
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                      theme: ExpandableThemeData(
                                        tapHeaderToExpand: true,
                                        tapBodyToExpand: false,
                                        tapBodyToCollapse: false,
                                        headerAlignment:
                                            ExpandablePanelHeaderAlignment.top,
                                        hasIcon: true,
                                        expandIcon:
                                            Icons.keyboard_arrow_down_sharp,
                                        collapseIcon:
                                            Icons.keyboard_arrow_up_outlined,
                                        iconColor: Color(0xFF030401),
                                        iconPadding: EdgeInsets.fromLTRB(
                                            0.0, 0.0, 12.0, 16.0),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 16.0, 0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                '1ekhxnxt' /* Related products */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 16.0),
                  child: Container(
                    width: double.infinity,
                    height: 260.0,
                    decoration: BoxDecoration(),
                    child: Padding(
                      padding:
                          EdgeInsetsDirectional.fromSTEB(5.0, 0.0, 5.0, 0.0),
                      child: GridView(
                        padding: EdgeInsets.zero,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 1,
                          crossAxisSpacing: 16.0,
                          mainAxisSpacing: 0.5,
                          childAspectRatio: 1.30,
                        ),
                        shrinkWrap: true,
                        scrollDirection: Axis.horizontal,
                        children: 
                         relatedPost.map((data){ 
                        
                         return GestureDetector(
                          onTap: (){
                                   context.pushNamed(
                                'ProductDetailPage',
                                queryParameters: {
                                  'post': jsonEncode(data.toJson()), // Serialize PostModel
                                  'related': 'true',
                                }.withoutNulls,
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: TransitionInfo(
                                    hasTransition: true,
                                    transitionType:
                                        PageTransitionType.rightToLeft,
                                    duration: Duration(milliseconds: 300),
                                  ),
                                },
                              );
                            
                          
                          },
                           child: Padding(
                             padding: const EdgeInsets.all(8.0),
                             child: Container(
                              decoration: BoxDecoration(
                                color:
                                    ClassifiedAppTheme.of(context).secondaryBackground,
                                boxShadow: [
                                  BoxShadow(
                                    blurRadius: 4.0,
                                    color: Color(0x27000000),
                                    offset: Offset(0.0, 4.0),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              child: Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    8.0, 8.0, 8.0, 8.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Stack(
                                      alignment: AlignmentDirectional(1.0, -1.0),
                                      children: [
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(8.0),
                                          child: CachedNetworkImage(
                                             width: double.infinity,
                                            height: 115.0,
                                            fit: BoxFit.cover,
                                              imageUrl:
                                                  "${ApiUtils.post_image}${data.image}",
                                              placeholder: (context, url) => Padding(
                                                padding: const EdgeInsets.all(35.0),
                                                child: Center(child: CircularProgressIndicator()),
                                              ),
                                              errorWidget: (context, url, error) =>
                                                  Icon(Icons.error),
                                            ),
                                           
                                        ),
                                        Align(
                                          alignment: AlignmentDirectional(1.0, -1.0),
                                          child: Padding(
                                            padding: EdgeInsetsDirectional.fromSTEB(
                                                0.0, 8.0, 8.0, 0.0),
                                            child: Container(
                                              width: 24.0,
                                              height: 24.0,
                                              decoration: BoxDecoration(
                                                color: ClassifiedAppTheme.of(context)
                                                    .primaryBackground,
                                                shape: BoxShape.circle,
                                              ),
                                              alignment:
                                                  AlignmentDirectional(0.0, 0.0),
                                              child: SvgPicture.asset(
                                                'assets/images/heart.svg',
                                                width: 13.0,
                                                height: 13.0,
                                                fit: BoxFit.contain,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0.0, 8.0, 0.0, 0.0),
                                      child: Text(
                                        "${data.post_name}",
                                        maxLines: 1,
                                        style: ClassifiedAppTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'Satoshi',
                                              fontSize: 15.0,
                                              fontWeight: FontWeight.bold,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0.0, 8.0, 0.0, 0.0),
                                      child: Text(
                                        "AED ${data.price}",
                                        style: ClassifiedAppTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'Satoshi',
                                              fontSize: 12.0,
                                              fontWeight: FontWeight.bold,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0.0, 8.0, 0.0, 0.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                         data.vendor_image ==''? ClipRRect(
                                          borderRadius: BorderRadius.circular(30.0),
                                            child: Image.asset(
                                              'assets/images/soho_icon.jpg',
                                              width: 32.0,
                                              height: 32.0,
                                            
                                              fit: BoxFit.contain,
                                            ),
                                          ):
                                           ClipRRect(
                                          borderRadius: BorderRadius.circular(30.0),
                                          child: CachedNetworkImage(
                                            
                                           width: 32.0,
                                              height: 32.0,
                                            fit: BoxFit.cover,
                                              imageUrl:
                                                  "${ApiUtils.profile_files}${data.vendor_image}",
                                              placeholder: (context, url) => Padding(
                                                padding: const EdgeInsets.all(35.0),
                                                child: Center(child: CircularProgressIndicator()),
                                              ),
                                              errorWidget: (context, url, error) =>
                                                  Icon(Icons.error),
                                            ),
                                           
                                        ),
                                          Padding(
                                            padding: EdgeInsetsDirectional.fromSTEB(
                                                8.0, 0.0, 0.0, 0.0),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "${data.vendor_name}",
                                                  style: ClassifiedAppTheme.of(context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily: 'Satoshi',
                                                        fontSize: 12.0,
                                                        fontWeight: FontWeight.bold,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                                Padding(
                                                  padding:
                                                      EdgeInsetsDirectional.fromSTEB(
                                                          0.0, 4.0, 0.0, 0.0),
                                                  child: Row(
                                                    mainAxisSize: MainAxisSize.max,
                                                    children: [
                                                      SvgPicture.asset(
                                                        'assets/images/location-home.svg',
                                                        width: 14.0,
                                                        height: 14.0,
                                                        fit: BoxFit.cover,
                                                      ),
                                                      Padding(
                                                        padding: EdgeInsetsDirectional
                                                            .fromSTEB(
                                                                4.0, 0.0, 0.0, 0.0),
                                                        child: Text(
                                                          "${data.city}",
                                                          style: ClassifiedAppTheme.of(
                                                                  context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily: 'Satoshi',
                                                                fontSize: 12.0,
                                                                fontWeight:
                                                                    FontWeight.w500,
                                                                useGoogleFonts: false,
                                                              ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                                                   ),
                           ),
                         );
                         }).toList()
                         
                        
                      ),
                    ),
                  ),
                ),
                        ]
                            .addToStart(SizedBox(height: 16.0))
                            .addToEnd(SizedBox(height: 30.0)),
                      ).animateOnPageLoad(
                          animationsMap['columnOnPageLoadAnimation']!),
                    ),
                  ].addToEnd(SizedBox(height: 70.0)),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(80.0, 0.0, 80.0, 24.0),
                child: wrapWithModel(
                  model: _model.appButtonModel,
                  updateCallback: () => setState(() {}),
                  child: AppButtonWidget(
                    text: FFLocalizations.of(context).getText(
                                          '4cs5qh4d' /* Contact seller */,
                                        ),
                    action: () async {
                      //   locationFromAddress('al jerf 2, ajman, uae')
                      //   .then((locations) {
                      // var output = 'No results found.';
                      // if (locations.isNotEmpty) {
                      //   output = locations[0].toString();
                      // }
                      // print("output ${output}");
                      // setState(() {
                      //   _output = output;
                      // });
                    // });
                     if(appStore.user_id !=null) {
                       // Validate required fields before opening bottom sheet
                       if (widget.post?.vendor_id == null || 
                           widget.post?.vendor_name == null || 
                           widget.post?.mobile == null) {
                         toasty(context, "Seller information is incomplete. Cannot contact seller.",
                             bgColor: Colors.red, textColor: Colors.white);
                         return;
                       }
                       
                       await showModalBottomSheet(
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        enableDrag: false,
                        context: context,
                        builder: (context) {
                          return GestureDetector(
                            onTap: () => _model.unfocusNode.canRequestFocus
                                ? FocusScope.of(context)
                                    .requestFocus(_model.unfocusNode)
                                : FocusScope.of(context).unfocus(),
                            child: Padding(
                              padding: MediaQuery.viewInsetsOf(context),
                              child: ContactUsBottomSheetWidget(
                                vendor_id: widget.post!.vendor_id!, 
                                vendor_name: widget.post!.vendor_name!, 
                                mobile: widget.post!.mobile!, 
                                show_mobile: widget.post!.show_mobile ?? 0, 
                                enable_sms: widget.post!.enable_sms ?? 0,
                                postName: widget.post!.post_name,
                                postId: widget.post!.id?.toString(),
                              ),
                            ),
                          );
                        },
                      ).then((value) => safeSetState(() {}));
                     } else {
                          toasty(context, "Invalid Access, Please Login!",
              bgColor: Colors.red, textColor: Colors.white);
                     }
                    },
                  ),
                ),
              ),
            ],
    
          ),
        ),
       floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        floatingActionButton: Visibility(
          visible: widget.related,  // Control visibility based on 'related' flag
          child: Stack(
            children: [
              Positioned(
                bottom: 80.0, // Move the button higher (50px from bottom)
                right: 0.0,  // Move the button towards right (0px for edge)
                child: FloatingActionButton(
                  onPressed: () {
                    // Your action here
                     context.goNamed(
                'HomeBottomBarPage',
                extra: <String, dynamic>{
                  kTransitionInfoKey: TransitionInfo(
                    hasTransition: true,
                    transitionType: PageTransitionType.rightToLeft,
                    duration: Duration(milliseconds: 300),
                  ),
                },
              );
                  },
                  backgroundColor: Colors.white54,
                  child: Icon(
                    Icons.home,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      
      ),
    );
  }
}

// class HeroPhotoViewWrapper extends StatelessWidget {
//   const HeroPhotoViewWrapper(
//       {this.imageProvider,
//       this.loadingChild,
//       this.backgroundDecoration,
//       this.minScale,
//       this.maxScale});

//   final ImageProvider? imageProvider;
//   final Widget? loadingChild;
//   final Decoration? backgroundDecoration;
//   final dynamic minScale;
//   final dynamic maxScale;

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       constraints: BoxConstraints.expand(
//         height: MediaQuery.of(context).size.height,
//       ),
//       child: PhotoView(
//         imageProvider: imageProvider,
//         //loadingChild: loadingChild,
//         backgroundDecoration: backgroundDecoration as BoxDecoration?,
//         minScale: minScale,
//         maxScale: maxScale,
//         heroAttributes: const PhotoViewHeroAttributes(tag: "hero_zoom"),
//       ),
//     );
//   }
// }