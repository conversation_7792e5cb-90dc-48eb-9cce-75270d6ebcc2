import 'package:soho_souk/Classified_App/classified_app_util.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/CityModel.dart';

import '../../Classified_App/classified_app_drop_down.dart';
import '../../Classified_App/classified_app_radio_button.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/form_field_controller.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'package:flutter/material.dart';
import 'filter_page_model.dart';
export 'filter_page_model.dart';

class FilterPageWidget extends StatefulWidget {
  const FilterPageWidget({super.key});

  @override
  State<FilterPageWidget> createState() => _FilterPageWidgetState();
}

class _FilterPageWidgetState extends State<FilterPageWidget> {
  late FilterPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => FilterPageModel());

    // Initialize filter values from AppStore
    _initializeFilterValues();
  }

  void _initializeFilterValues() {
    // Initialize sort by radio button
    if (appStore.currentSortBy != null) {
      _model.radioButtonValueController1 =
          FormFieldController<String>(appStore.currentSortBy!);
    } else {
      _model.radioButtonValueController1 =
          FormFieldController<String>("Newest post");
    }

    // Initialize price range radio button
    if (appStore.currentPriceRange != null) {
      _model.radioButtonValueController2 =
          FormFieldController<String>(appStore.currentPriceRange!);
    } else {
      _model.radioButtonValueController2 =
          FormFieldController<String>("Low to High");
    }

    // Initialize barter filter radio button
    if (appStore.currentBarterFilter != null) {
      _model.radioButtonValueController3 =
          FormFieldController<String>(appStore.currentBarterFilter!);
    } else {
      _model.radioButtonValueController3 = FormFieldController<String>("No");
    }

    // Initialize condition dropdown - always create controller
    if (appStore.currentCondition != null) {
      _model.dropDownValueController4 =
          FormFieldController<String>(appStore.currentCondition!);
      _model.dropDownValue4 = appStore.currentCondition!;
    } else {
      _model.dropDownValueController4 = FormFieldController<String>(null);
    }

    // Initialize city dropdown - always create controller
    if (appStore.currentCity != null) {
      _model.dropDownValueController2 =
          FormFieldController<String>(appStore.currentCity!);
      _model.dropDownValue2 = appStore.currentCity!;
      // Load areas for the selected city
      getArea(appStore.currentCity!);
    } else {
      _model.dropDownValueController2 = FormFieldController<String>(null);
    }

    // Initialize area dropdown - always create controller
    if (appStore.currentArea != null) {
      _model.dropDownValueController3 =
          FormFieldController<String>(appStore.currentArea!);
      _model.dropDownValue3 = appStore.currentArea!;
    } else {
      _model.dropDownValueController3 = FormFieldController<String>(null);
    }

    // Initialize the first dropdown controller if not already done
    if (_model.dropDownValueController1 == null) {
      _model.dropDownValueController1 = FormFieldController<String>(null);
    }
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  List<CityModels> childCity = [];
  getArea(val) {
    List<CityModels> selectCity =
        appStore.mainCity.where((i) => i.city == val).toList();
    setState(() {
      childCity = [];
      if (selectCity.isNotEmpty && selectCity[0].id != null) {
        final parentId = selectCity[0].id!;
        childCity = appStore.city
            .where((i) => i.parent_id == parentId)
            .toList();
      }
      // Reset area selection when city changes
      _model.dropDownValueController3?.reset();
      _model.dropDownValue3 = null;
    });
  }

  void submitFilter() {
    // Apply filters to AppStore first
    appStore.applyFilters(
      sortBy: _model.radioButtonValueController1?.value,
      priceRange: _model.radioButtonValueController2?.value,
      barterFilter: _model.radioButtonValueController3?.value,
      condition: _model.dropDownValueController4?.value,
      city: _model.dropDownValueController2?.value,
      area: _model.dropDownValueController3?.value,
    );

    // Return filter values for backward compatibility
    Navigator.pop(context, {
      'sort': _model.radioButtonValueController1?.value,
      'priceRange': _model.radioButtonValueController2?.value,
      'barterFilter': _model.radioButtonValueController3?.value,
      'conditions': _model.dropDownValueController4?.value,
      'city': _model.dropDownValueController2?.value,
      'area': _model.dropDownValueController3?.value,
    });
  }

  void resetFilters() {
    // Clear filters in AppStore first
    appStore.clearFilters();

    setState(() {
      // Reset local state
      _model.dropDownValue1 = null;
      _model.dropDownValue2 = null;
      _model.dropDownValue3 = null;
      _model.dropDownValue4 = null;
      childCity = [];

      // Reset controllers to default values without disposing
      _model.radioButtonValueController1?.reset();
      _model.radioButtonValueController2?.reset();
      _model.radioButtonValueController3?.reset();
      _model.dropDownValueController1?.reset();
      _model.dropDownValueController2?.reset();
      _model.dropDownValueController3?.reset();
      _model.dropDownValueController4?.reset();

      // Set default values for radio buttons
      _model.radioButtonValueController1?.value = "Newest post";
      _model.radioButtonValueController2?.value = "Low to High";
      _model.radioButtonValueController3?.value = "No";
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.appBarModel,
                updateCallback: () => setState(() {}),
                child: AppBarWidget(
                  title: "Filter",
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          FFLocalizations.of(context).getText(
                            '3rrgx5n9' /* Sorting */,
                          ),
                          style: ClassifiedAppTheme.of(context)
                              .bodyMedium
                              .override(
                                fontFamily: 'Satoshi',
                                fontSize: 18.0,
                                fontWeight: FontWeight.bold,
                                useGoogleFonts: false,
                              ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 13.0, 0.0, 0.0),
                          child: ClassifiedAppRadioButton(
                            options: ["Newest post", "Featured post"].toList(),
                            onChanged: (val) => setState(() {
                              // Handle the selected value
                              print("Selected: $val");
                            }),
                            controller: _model.radioButtonValueController1!,
                            optionHeight:
                                32.0, // Increased height for better visibility
                            textStyle: ClassifiedAppTheme.of(context)
                                .labelMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryText,
                                  fontSize: 16.0, // Slightly smaller font
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            textPadding: EdgeInsetsDirectional.fromSTEB(4.0,
                                0.0, 4.0, 0.0), // Reduced horizontal spacing
                            buttonPosition: RadioButtonPosition.left,
                            direction: Axis
                                .vertical, // Changed to vertical layout for better visibility
                            radioButtonColor:
                                ClassifiedAppTheme.of(context).primary,
                            inactiveRadioButtonColor:
                                ClassifiedAppTheme.of(context).info,
                            toggleable: false,
                            horizontalAlignment: WrapAlignment
                                .start, // Align to start for vertical layout
                            verticalAlignment: WrapCrossAlignment
                                .start, // Align items to start
                          ),
                        ),

                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            "Barter Item",
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 13.0, 0.0, 0.0),
                          child: _model.radioButtonValueController3 != null
                              ? ClassifiedAppRadioButton(
                                  options: ["No", "Yes"].toList(),
                                  onChanged: (val) => setState(() {
                                    // Handle the selected value
                                    print("Barter filter selected: $val");
                                  }),
                                  controller:
                                      _model.radioButtonValueController3!,
                                  optionHeight: 32.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .primaryText,
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  textPadding: EdgeInsetsDirectional.fromSTEB(
                                      4.0, 0.0, 4.0, 0.0),
                                  buttonPosition: RadioButtonPosition.left,
                                  direction: Axis.vertical,
                                  radioButtonColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  inactiveRadioButtonColor:
                                      ClassifiedAppTheme.of(context).info,
                                  toggleable: false,
                                  horizontalAlignment: WrapAlignment.start,
                                  verticalAlignment: WrapCrossAlignment.start,
                                )
                              : SizedBox(), // Show empty widget if controller is null
                        ),

                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            "Price Range",
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 12.0, 0.0, 0.0),
                          child: ClassifiedAppRadioButton(
                            options: ["Low to High", "High to Low"].toList(),
                            onChanged: (val) => setState(() {}),
                            controller: _model.radioButtonValueController2!,
                            optionHeight:
                                32.0, // Increased height for consistency
                            textStyle: ClassifiedAppTheme.of(context)
                                .labelMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryText,
                                  fontSize: 16.0, // Consistent font size
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            textPadding: EdgeInsetsDirectional.fromSTEB(
                                4.0, 0.0, 4.0, 0.0), // Consistent padding
                            buttonPosition: RadioButtonPosition.left,
                            direction: Axis
                                .vertical, // Changed to vertical for consistency
                            radioButtonColor:
                                ClassifiedAppTheme.of(context).primary,
                            inactiveRadioButtonColor:
                                ClassifiedAppTheme.of(context).info,
                            toggleable: false,
                            horizontalAlignment: WrapAlignment.start,
                            verticalAlignment: WrapCrossAlignment.start,
                          ),
                        ),
                        // Padding(
                        //   padding: EdgeInsetsDirectional.fromSTEB(
                        //       0.0, 16.0, 0.0, 0.0),
                        //   child: Text(
                        //     FFLocalizations.of(context).getText(
                        //       'j1zmirr1' /* Price range */,
                        //     ),
                        //     style: ClassifiedAppTheme.of(context)
                        //         .bodyMedium
                        //         .override(
                        //           fontFamily: 'Satoshi',
                        //           fontSize: 18.0,
                        //           fontWeight: FontWeight.bold,
                        //           useGoogleFonts: false,
                        //         ),
                        //   ),
                        // ),
                        // Padding(
                        //   padding: EdgeInsetsDirectional.fromSTEB(
                        //       0.0, 16.0, 0.0, 0.0),
                        //   child: Row(
                        //     mainAxisSize: MainAxisSize.max,
                        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //     children: [
                        //       Expanded(
                        //         child: Padding(
                        //           padding: EdgeInsetsDirectional.fromSTEB(
                        //               0.0, 0.0, 8.0, 0.0),
                        //           child: ClassifiedAppDropDown<String>(
                        //             controller:
                        //                 _model.dropDownValueController1 ??=
                        //                     FormFieldController<String>(null),
                        //             options: numberList(),
                        //             onChanged: (val) => setState(
                        //                 () => _model.dropDownValue1 = val),
                        //             height: 54.0,
                        //             textStyle: ClassifiedAppTheme.of(context)
                        //                 .bodyMedium
                        //                 .override(
                        //                   fontFamily: 'Satoshi',
                        //                   fontSize: 17.0,
                        //                   fontWeight: FontWeight.w500,
                        //                   useGoogleFonts: false,
                        //                 ),
                        //             hintText:
                        //                 FFLocalizations.of(context).getText(
                        //               '48u4qvyx' /* Min */,
                        //             ),
                        //             icon: Icon(
                        //               Icons.keyboard_arrow_down_rounded,
                        //               color: ClassifiedAppTheme.of(context)
                        //                   .secondaryText,
                        //               size: 24.0,
                        //             ),
                        //             fillColor: ClassifiedAppTheme.of(context)
                        //                 .secondaryBackground,
                        //             elevation: 2.0,
                        //             borderColor:
                        //                 ClassifiedAppTheme.of(context).info,
                        //             borderWidth: 1.0,
                        //             borderRadius: 8.0,
                        //             margin: EdgeInsetsDirectional.fromSTEB(
                        //                 16.0, 4.0, 16.0, 4.0),
                        //             hidesUnderline: true,
                        //             isOverButton: true,
                        //             isSearchable: false,
                        //             isMultiSelect: false,
                        //           ),
                        //         ),
                        //       ),
                        //       Expanded(
                        //         child: Padding(
                        //           padding: EdgeInsetsDirectional.fromSTEB(
                        //               8.0, 0.0, 0.0, 0.0),
                        //           child: ClassifiedAppDropDown<String>(
                        //             controller:
                        //                 _model.dropDownValueController2 ??=
                        //                     FormFieldController<String>(null),
                        //             options: numberList(),
                        //             onChanged: (val) => setState(
                        //                 () => _model.dropDownValue2 = val),
                        //             height: 54.0,
                        //             textStyle: ClassifiedAppTheme.of(context)
                        //                 .bodyMedium
                        //                 .override(
                        //                   fontFamily: 'Satoshi',
                        //                   fontSize: 17.0,
                        //                   fontWeight: FontWeight.w500,
                        //                   useGoogleFonts: false,
                        //                 ),
                        //             hintText:
                        //                 FFLocalizations.of(context).getText(
                        //               '04ol3m5r' /* Max */,
                        //             ),
                        //             icon: Icon(
                        //               Icons.keyboard_arrow_down_rounded,
                        //               color: ClassifiedAppTheme.of(context)
                        //                   .secondaryText,
                        //               size: 24.0,
                        //             ),
                        //             fillColor: ClassifiedAppTheme.of(context)
                        //                 .secondaryBackground,
                        //             elevation: 2.0,
                        //             borderColor:
                        //                 ClassifiedAppTheme.of(context).info,
                        //             borderWidth: 1.0,
                        //             borderRadius: 8.0,
                        //             margin: EdgeInsetsDirectional.fromSTEB(
                        //                 16.0, 4.0, 16.0, 4.0),
                        //             hidesUnderline: true,
                        //             isOverButton: true,
                        //             isSearchable: false,
                        //             isMultiSelect: false,
                        //           ),
                        //         ),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'unique_key_1' /* Item Conditions */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 12.0, 0.0, 0.0),
                          child: ClassifiedAppDropDown<String>(
                            controller: _model.dropDownValueController4 ??=
                                FormFieldController<String>(null),
                            options: appStore.condition
                                .map((e) => e.toString())
                                .toList(),
                            onChanged: (val) =>
                                setState(() => _model.dropDownValue4 = val),
                            width: double.infinity,
                            height: 54.0,
                            textStyle: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            hintText: FFLocalizations.of(context).getText(
                              'unique_key_1' /* Item Conditions */,
                            ),
                            icon: Icon(
                              Icons.keyboard_arrow_right_sharp,
                              color:
                                  ClassifiedAppTheme.of(context).secondaryText,
                              size: 24.0,
                            ),
                            fillColor: ClassifiedAppTheme.of(context)
                                .secondaryBackground,
                            elevation: 2.0,
                            borderColor: ClassifiedAppTheme.of(context).info,
                            borderWidth: 1.0,
                            borderRadius: 8.0,
                            margin: EdgeInsetsDirectional.fromSTEB(
                                16.0, 4.0, 16.0, 4.0),
                            hidesUnderline: true,
                            isOverButton: true,
                            isSearchable: false,
                            isMultiSelect: false,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'bxekoq73' /* Select City */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 12.0, 0.0, 0.0),
                          child: ClassifiedAppDropDown<String>(
                            controller: _model.dropDownValueController2 ??=
                                FormFieldController<String>(null),
                            options: appStore.mainCity
                                .map((data) => data.city?.toString() ?? '')
                                .where((city) => city.isNotEmpty)
                                .toList(),
                            onChanged: (val) {
                              getArea(val);
                              setState(() => _model.dropDownValue2 = val);
                            },
                            width: double.infinity,
                            height: 54.0,
                            textStyle: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            hintText: FFLocalizations.of(context).getText(
                              'bxekoq73' /* Select City */,
                            ),
                            icon: Icon(
                              Icons.keyboard_arrow_right_sharp,
                              color:
                                  ClassifiedAppTheme.of(context).secondaryText,
                              size: 24.0,
                            ),
                            fillColor: ClassifiedAppTheme.of(context)
                                .secondaryBackground,
                            elevation: 2.0,
                            borderColor: ClassifiedAppTheme.of(context).info,
                            borderWidth: 1.0,
                            borderRadius: 8.0,
                            margin: EdgeInsetsDirectional.fromSTEB(
                                16.0, 4.0, 16.0, 4.0),
                            hidesUnderline: true,
                            isOverButton: true,
                            isSearchable: false,
                            isMultiSelect: false,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 16.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'unique_key_2' /* Select Area */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 12.0, 0.0, 0.0),
                          child: ClassifiedAppDropDown<String>(
                            controller: _model.dropDownValueController3 ??=
                                FormFieldController<String>(null),
                            options: childCity
                                .map((data) => data.city?.toString() ?? '')
                                .where((city) => city.isNotEmpty)
                                .toList(),
                            onChanged: (val) =>
                                setState(() => _model.dropDownValue3 = val),
                            width: double.infinity,
                            height: 54.0,
                            textStyle: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            hintText: "",
                            icon: Icon(
                              Icons.keyboard_arrow_right_sharp,
                              color:
                                  ClassifiedAppTheme.of(context).secondaryText,
                              size: 24.0,
                            ),
                            fillColor: ClassifiedAppTheme.of(context)
                                .secondaryBackground,
                            elevation: 2.0,
                            borderColor: ClassifiedAppTheme.of(context).info,
                            borderWidth: 1.0,
                            borderRadius: 8.0,
                            margin: EdgeInsetsDirectional.fromSTEB(
                                16.0, 4.0, 16.0, 4.0),
                            hidesUnderline: true,
                            isOverButton: true,
                            isSearchable: false,
                            isMultiSelect: false,
                          ),
                        ),
                      ]
                          .addToStart(SizedBox(height: 16.0))
                          .addToEnd(SizedBox(height: 24.0)),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 32.0, 16.0, 20.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 8.0, 0.0),
                        child: InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            resetFilters();
                          },
                          child: Container(
                            height: 56.0,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context)
                                  .primaryBackground,
                              borderRadius: BorderRadius.circular(12.0),
                              border: Border.all(
                                color: ClassifiedAppTheme.of(context).primary,
                              ),
                            ),
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                'a52qayl8' /* Reset */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color:
                                        ClassifiedAppTheme.of(context).primary,
                                    fontSize: 18.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 0.0, 0.0),
                        child: InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            // context.safePop();
                            submitFilter();
                          },
                          child: Container(
                            height: 56.0,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).primary,
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                'lq0hnexl' /* Apply */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .primaryBackground,
                                    fontSize: 18.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
