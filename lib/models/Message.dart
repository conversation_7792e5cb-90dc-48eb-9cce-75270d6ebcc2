import 'package:cloud_firestore/cloud_firestore.dart';

class Message {
  final String? sender_id, reciver_id, message, user_post_message_id;
  final Timestamp? timestamp;
  final bool? isRead;

  late bool? fromMe;
  // bool showTime = true;

  Message(
      {this.message,
      this.reciver_id,
      this.fromMe,
      this.sender_id,
      this.timestamp,
      this.user_post_message_id,
      this.isRead = false});
  
  Map<String, dynamic> toMap() {
    return {
      "sender_id": sender_id,
      "reciver_id": reciver_id,
      "message": message,
      "timestamp": timestamp,
      "user_post_message_id": user_post_message_id,
      "isRead": isRead,
    };
  }


}
