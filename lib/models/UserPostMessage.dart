import 'package:cloud_firestore/cloud_firestore.dart';

class UserPostMessage {
  final String? id;
  final String? postName;
  final String? postId;
  final String? roomId;
  final String? user1Name;
  final String? user1Id;
  final String? user1FCMToken;
  final String? user2Name;
  final String? user2Id;
  final String? user2FCMToken;
  final Timestamp? createdAt;
  final Timestamp? lastMessageTime;
  final String? lastMessage;
  final int? unreadCountUser1;
  final int? unreadCountUser2;

  UserPostMessage({
    this.id,
    this.postName,
    this.postId,
    this.roomId,
    this.user1Name,
    this.user1Id,
    this.user1FCMToken,
    this.user2Name,
    this.user2Id,
    this.user2FCMToken,
    this.createdAt,
    this.lastMessageTime,
    this.lastMessage,
    this.unreadCountUser1 = 0,
    this.unreadCountUser2 = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      "postName": postName,
      "postId": postId,
      "roomId": roomId,
      "user1Name": user1Name,
      "user1Id": user1Id,
      "user1FCMToken": user1FCMToken,
      "user2Name": user2Name,
      "user2Id": user2Id,
      "user2FCMToken": user2FCMToken,
      "createdAt": createdAt,
      "lastMessageTime": lastMessageTime,
      "lastMessage": lastMessage,
      "unreadCountUser1": unreadCountUser1,
      "unreadCountUser2": unreadCountUser2,
    };
  }

  factory UserPostMessage.fromMap(Map<String, dynamic> map, String documentId) {
    return UserPostMessage(
      id: documentId,
      postName: map['postName'],
      postId: map['postId'],
      roomId: map['roomId'],
      user1Name: map['user1Name'],
      user1Id: map['user1Id'],
      user1FCMToken: map['user1FCMToken'],
      user2Name: map['user2Name'],
      user2Id: map['user2Id'],
      user2FCMToken: map['user2FCMToken'],
      createdAt: map['createdAt'],
      lastMessageTime: map['lastMessageTime'],
      lastMessage: map['lastMessage'],
      unreadCountUser1: map['unreadCountUser1'] ?? 0,
      unreadCountUser2: map['unreadCountUser2'] ?? 0,
    );
  }
}